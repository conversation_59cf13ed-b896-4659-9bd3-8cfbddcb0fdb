<?php

namespace App\Services;

use App\Models\WhatsappGatewayConfig;
use Illuminate\Support\Facades\Log;
use Exception;

class WhatsappGatewayService
{
    protected $config;

    public function __construct()
    {
        $this->config = WhatsappGatewayConfig::where('is_active', true)->first();
    }

    /**
     * Send WhatsApp message
     */
    public function sendMessage($phoneNumber, $message, $imageUrl = null, $documentUrl = null)
    {
        if (!$this->config) {
            throw new Exception('WhatsApp Gateway configuration not found or inactive');
        }

        // Clean phone number (remove non-numeric characters except +)
        $cleanPhone = $this->cleanPhoneNumber($phoneNumber);

        $data = [
            'token' => $this->config->device_token,
            'inquiry' => 'send-messages',
            'receiptnumber' => $cleanPhone,
            'message' => $message,
            'apikey' => $this->config->api_key
        ];

        // Add optional parameters
        if ($imageUrl) {
            $data['imageurl'] = $imageUrl;
        }

        if ($documentUrl) {
            $data['documenturl'] = $documentUrl;
        }

        try {
            $response = $this->makeApiCall($data);
            
            Log::info('WhatsApp message sent', [
                'phone' => $cleanPhone,
                'message_length' => strlen($message),
                'response' => $response
            ]);

            return $response;
        } catch (Exception $e) {
            Log::error('WhatsApp message failed', [
                'phone' => $cleanPhone,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Send retribusi notification
     */
    public function sendRetribusiNotification($retribusi, $type = 'created')
    {
        if (!$retribusi->company || !$retribusi->company->telepon) {
            throw new Exception('Company phone number not found');
        }

        $message = $this->buildRetribusiMessage($retribusi, $type);
        
        return $this->sendMessage(
            $retribusi->company->telepon,
            $message
        );
    }

    /**
     * Send payment status notification
     */
    public function sendPaymentStatusNotification($retribusi, $statusType, $transactionDetails = null)
    {
        if (!$retribusi->company || !$retribusi->company->telepon) {
            throw new Exception('Company phone number not found');
        }

        $message = $this->buildPaymentStatusMessage($retribusi, $statusType, $transactionDetails);
        
        return $this->sendMessage(
            $retribusi->company->telepon,
            $message
        );
    }

    /**
     * Build retribusi message
     */
    private function buildRetribusiMessage($retribusi, $type)
    {
        $statusEmoji = [
            'created' => '📋',
            'updated' => '🔄',
            'cancelled' => '❌'
        ];

        $emoji = $statusEmoji[$type] ?? '📋';
        
        $message = "{$emoji} *RETRIMARIN - Notifikasi Retribusi*\n\n";
        
        if ($type === 'created') {
            $message .= "Retribusi baru telah dibuat:\n\n";
        } elseif ($type === 'updated') {
            $message .= "Retribusi telah diperbarui:\n\n";
        } else {
            $message .= "Retribusi telah dibatalkan:\n\n";
        }

        $message .= "📄 *Nomor:* {$retribusi->nomor_retribusi}\n";
        $message .= "🚢 *Kapal:* {$retribusi->kapal->nama_kapal}\n";
        $message .= "🏢 *Perusahaan:* " . ($retribusi->company ? $retribusi->company->nama_perusahaan : 'Pribadi') . "\n";
        $message .= "📊 *Tarif:* {$retribusi->tarif->nama_tarif}\n";
        $message .= "📦 *Jumlah:* {$retribusi->jumlah} {$retribusi->tarif->satuan}\n";
        $message .= "💰 *Total:* " . $retribusi->formatted_total_harga . "\n";
        $message .= "📅 *Tanggal:* " . $retribusi->tanggal_transaksi->format('d/m/Y H:i') . "\n";
        $message .= "👤 *Petugas:* {$retribusi->petugas->nama_petugas}\n";
        $message .= "💳 *Pembayaran:* {$retribusi->metodePembayaran->nama_metode}\n";

        if ($retribusi->keterangan) {
            $message .= "📝 *Keterangan:* {$retribusi->keterangan}\n";
        }

        $message .= "\n---\n";
        $message .= "Sistem Manajemen Retribusi Pelabuhan\n";
        $message .= "🌐 Retrimarin";

        return $message;
    }

    /**
     * Build payment status message
     */
    private function buildPaymentStatusMessage($retribusi, $statusType, $transactionDetails = null)
    {
        $statusConfig = [
            'success' => ['emoji' => '✅', 'title' => 'Pembayaran Berhasil'],
            'cancelled' => ['emoji' => '⚠️', 'title' => 'Pembayaran Dibatalkan'],
            'failed' => ['emoji' => '❌', 'title' => 'Pembayaran Gagal']
        ];

        $config = $statusConfig[$statusType] ?? $statusConfig['failed'];
        
        $message = "{$config['emoji']} *RETRIMARIN - {$config['title']}*\n\n";
        
        if ($statusType === 'success') {
            $message .= "Pembayaran untuk retribusi berikut telah berhasil diproses:\n\n";
        } elseif ($statusType === 'cancelled') {
            $message .= "Pembayaran untuk retribusi berikut telah dibatalkan:\n\n";
        } else {
            $message .= "Pembayaran untuk retribusi berikut mengalami kegagalan:\n\n";
        }

        $message .= "📄 *Nomor:* {$retribusi->nomor_retribusi}\n";
        $message .= "🚢 *Kapal:* {$retribusi->kapal->nama_kapal}\n";
        $message .= "💰 *Total:* " . $retribusi->formatted_total_harga . "\n";
        $message .= "📊 *Status:* {$retribusi->status_pembayaran_label}\n";

        if ($retribusi->tanggal_pembayaran) {
            $message .= "📅 *Tgl Bayar:* " . $retribusi->tanggal_pembayaran->format('d/m/Y H:i') . "\n";
        }

        if ($transactionDetails && isset($transactionDetails->payment_type)) {
            $message .= "💳 *Metode:* " . ucfirst(str_replace('_', ' ', $transactionDetails->payment_type)) . "\n";
        }

        $message .= "\n";

        if ($statusType === 'success') {
            $message .= "✅ Pembayaran telah berhasil diproses\n";
            $message .= "✅ Retribusi telah lunas\n";
            $message .= "✅ Simpan pesan ini sebagai bukti\n";
        } elseif ($statusType === 'cancelled') {
            $message .= "⚠️ Pembayaran telah dibatalkan\n";
            $message .= "ℹ️ Hubungi petugas untuk bantuan\n";
            $message .= "ℹ️ Tidak ada biaya yang dikenakan\n";
        } else {
            $message .= "❌ Pembayaran mengalami kegagalan\n";
            $message .= "🔄 Silakan coba lagi\n";
            $message .= "📞 Hubungi petugas jika masalah berlanjut\n";
        }

        $message .= "\n---\n";
        $message .= "Sistem Manajemen Retribusi Pelabuhan\n";
        $message .= "🌐 Retrimarin";

        return $message;
    }

    /**
     * Clean phone number
     */
    private function cleanPhoneNumber($phoneNumber)
    {
        // Remove all non-numeric characters except +
        $cleaned = preg_replace('/[^0-9+]/', '', $phoneNumber);
        
        // Convert Indonesian format to international
        if (substr($cleaned, 0, 1) === '0') {
            $cleaned = '62' . substr($cleaned, 1);
        } elseif (substr($cleaned, 0, 1) === '+') {
            $cleaned = substr($cleaned, 1);
        }
        
        return $cleaned;
    }

    /**
     * Make API call to WhatsApp Gateway
     */
    private function makeApiCall($data)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->config->base_url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);

        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new Exception('cURL Error: ' . $error);
        }

        if ($httpCode !== 200) {
            throw new Exception('HTTP Error: ' . $httpCode . ' - ' . $result);
        }

        $response = json_decode($result, true);
        
        if (!$response || (isset($response['RESULT']) && $response['RESULT'] !== 'OK')) {
            throw new Exception('API Error: ' . ($response['MESSAGE'] ?? 'Unknown error'));
        }

        return $response;
    }

    /**
     * Check if WhatsApp Gateway is configured
     */
    public function isConfigured()
    {
        return $this->config !== null;
    }

    /**
     * Test connection
     */
    public function testConnection()
    {
        if (!$this->config) {
            throw new Exception('WhatsApp Gateway not configured');
        }

        // Send a simple test message to verify connection
        $testData = [
            'token' => $this->config->device_token,
            'inquiry' => 'device-status',
            'apikey' => $this->config->api_key
        ];

        return $this->makeApiCall($testData);
    }
}
