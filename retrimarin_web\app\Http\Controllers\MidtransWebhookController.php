<?php

namespace App\Http\Controllers;

use App\Models\Retribusi;
use App\Services\MidtransService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Exception;

class MidtransWebhookController extends Controller
{
    protected $midtransService;

    public function __construct()
    {
        $this->midtransService = new MidtransService();
    }

    /**
     * Handle Midtrans notification webhook
     */
    public function notification(Request $request)
    {
        try {
            // Get notification data
            $notificationData = $request->all();

            Log::info('Midtrans webhook received', $notificationData);

            // Verify notification authenticity
            if (!$this->verifySignature($request)) {
                Log::warning('Invalid Midtrans signature', $notificationData);
                return response()->json(['status' => 'error', 'message' => 'Invalid signature'], 400);
            }

            $orderId = $notificationData['order_id'] ?? null;
            $transactionStatus = $notificationData['transaction_status'] ?? null;
            $fraudStatus = $notificationData['fraud_status'] ?? null;

            if (!$orderId) {
                Log::error('Missing order_id in Midtrans notification', $notificationData);
                return response()->json(['status' => 'error', 'message' => 'Missing order_id'], 400);
            }

            // Find retribusi by nomor_retribusi
            $retribusi = Retribusi::where('nomor_retribusi', $orderId)->first();

            if (!$retribusi) {
                Log::warning('Retribusi not found for order_id: ' . $orderId, $notificationData);
                return response()->json(['status' => 'error', 'message' => 'Order not found'], 404);
            }

            // Get transaction status from Midtrans
            $transactionDetails = $this->midtransService->getTransactionStatus($orderId);

            // Update retribusi status based on transaction status
            $this->updateRetribusiStatus($retribusi, $transactionDetails);

            Log::info('Retribusi status updated', [
                'retribusi_id' => $retribusi->id,
                'order_id' => $orderId,
                'old_status' => $retribusi->status_pembayaran,
                'new_status' => $this->mapMidtransStatus($transactionDetails->transaction_status, $transactionDetails->fraud_status ?? null),
                'transaction_details' => $transactionDetails
            ]);

            return response()->json(['status' => 'success']);
        } catch (Exception $e) {
            Log::error('Midtrans webhook error', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json(['status' => 'error', 'message' => 'Internal server error'], 500);
        }
    }

    /**
     * Verify Midtrans signature
     */
    private function verifySignature(Request $request)
    {
        if (!$this->midtransService->isConfigured()) {
            return false;
        }

        $orderId = $request->input('order_id');
        $statusCode = $request->input('status_code');
        $grossAmount = $request->input('gross_amount');
        // Get server key from config
        $config = \App\Models\PaymentGatewayConfig::where('gateway_name', 'midtrans')
            ->where('is_active', true)
            ->first();

        if (!$config) {
            return false;
        }

        $serverKey = $config->server_key;

        $signatureKey = hash('sha512', $orderId . $statusCode . $grossAmount . $serverKey);
        $receivedSignature = $request->input('signature_key');

        return $signatureKey === $receivedSignature;
    }

    /**
     * Update retribusi status based on Midtrans transaction status
     */
    private function updateRetribusiStatus(Retribusi $retribusi, $transactionDetails)
    {
        $newStatus = $this->mapMidtransStatus(
            $transactionDetails->transaction_status,
            $transactionDetails->fraud_status ?? null
        );

        $updateData = ['status_pembayaran' => $newStatus];

        // Set payment date if transaction is successful
        if ($newStatus === 'lunas') {
            $updateData['tanggal_pembayaran'] = now();
        }

        $retribusi->update($updateData);
    }

    /**
     * Map Midtrans transaction status to retribusi status
     */
    private function mapMidtransStatus($transactionStatus, $fraudStatus = null)
    {
        switch ($transactionStatus) {
            case 'capture':
                return ($fraudStatus === 'challenge') ? 'pending' : 'lunas';
            case 'settlement':
                return 'lunas';
            case 'pending':
                return 'pending';
            case 'deny':
            case 'cancel':
            case 'expire':
                return 'batal';
            default:
                return 'pending';
        }
    }

    /**
     * Manual check transaction status (for testing)
     */
    public function checkStatus(Request $request)
    {
        try {
            $orderId = $request->input('order_id');

            if (!$orderId) {
                return response()->json(['error' => 'order_id is required'], 400);
            }

            $transactionDetails = $this->midtransService->getTransactionStatus($orderId);

            return response()->json([
                'status' => 'success',
                'data' => $transactionDetails
            ]);
        } catch (Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
