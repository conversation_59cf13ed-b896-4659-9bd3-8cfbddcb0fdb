@extends('layouts.app')

@section('title', 'Konfigurasi Payment Gateway')

@section('content')
    <div class="container-fluid">
        <div class="card bg-light-info shadow-none position-relative overflow-hidden">
            <div class="card-body px-4 py-3">
                <div class="row align-items-center">
                    <div class="col-9">
                        <h4 class="fw-semibold mb-8">Konfigurasi Payment Gateway</h4>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a class="text-muted"
                                        href="{{ route('dashboard') }}">Dashboard</a>
                                </li>
                                <li class="breadcrumb-item" aria-current="page">Konfigurasi Payment Gateway</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="col-3">
                        <div class="text-center mb-n5">
                            <img src="{{ asset('package/dist/images/breadcrumb/ChatBc.png') }}" alt=""
                                class="img-fluid mb-n4">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @if (session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                {{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        @if (session('error'))
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                {{ session('error') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-body">
                        <div class="mb-4">
                            <h5 class="card-title fw-semibold">Konfigurasi Payment Gateway</h5>
                            <p class="card-subtitle mb-0">Atur konfigurasi payment gateway untuk sistem pembayaran
                                retribusi.
                                Konfigurasi ini akan digunakan untuk semua transaksi pembayaran melalui gateway.</p>
                        </div>

                        <form action="{{ route('payment-gateway-config.update') }}" method="POST">
                            @csrf
                            <div class="row">
                                <div class="col-lg-8">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="card-title mb-0">Informasi Gateway</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="gateway_name" class="form-label">Nama Gateway <span
                                                                class="text-danger">*</span></label>
                                                        <select
                                                            class="form-select @error('gateway_name') is-invalid @enderror"
                                                            id="gateway_name" name="gateway_name" required>
                                                            <option value="">Pilih Gateway</option>
                                                            <option value="midtrans"
                                                                {{ old('gateway_name', $config->gateway_name ?? '') == 'midtrans' ? 'selected' : '' }}>
                                                                Midtrans</option>
                                                        </select>
                                                        @error('gateway_name')
                                                            <div class="invalid-feedback">{{ $message }}</div>
                                                        @enderror
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="merchant_id" class="form-label">Merchant ID <span
                                                                class="text-danger">*</span></label>
                                                        <input type="text"
                                                            class="form-control @error('merchant_id') is-invalid @enderror"
                                                            id="merchant_id" name="merchant_id"
                                                            value="{{ old('merchant_id', $config->merchant_id ?? '') }}"
                                                            placeholder="Masukkan Merchant ID" required>
                                                        @error('merchant_id')
                                                            <div class="invalid-feedback">{{ $message }}</div>
                                                        @enderror
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="client_key" class="form-label">Client Key <span
                                                                class="text-danger">*</span></label>
                                                        <input type="text"
                                                            class="form-control @error('client_key') is-invalid @enderror"
                                                            id="client_key" name="client_key"
                                                            value="{{ old('client_key', $config->client_key ?? '') }}"
                                                            placeholder="Masukkan Client Key" required>
                                                        @error('client_key')
                                                            <div class="invalid-feedback">{{ $message }}</div>
                                                        @enderror
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="server_key" class="form-label">Server Key <span
                                                                class="text-danger">*</span></label>
                                                        <input type="text"
                                                            class="form-control @error('server_key') is-invalid @enderror"
                                                            id="server_key" name="server_key"
                                                            value="{{ old('server_key', $config->server_key ?? '') }}"
                                                            placeholder="Masukkan Server Key" required>
                                                        @error('server_key')
                                                            <div class="invalid-feedback">{{ $message }}</div>
                                                        @enderror
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="mb-3">
                                                <label for="description" class="form-label">Deskripsi</label>
                                                <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description"
                                                    rows="3" placeholder="Masukkan deskripsi konfigurasi (opsional)">{{ old('description', $config->description ?? '') }}</textarea>
                                                @error('description')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-lg-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="card-title mb-0">Pengaturan</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label class="form-label">Environment</label>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="is_production"
                                                        id="sandbox" value="0"
                                                        {{ old('is_production', $config->is_production ?? 0) == 0 ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="sandbox">
                                                        Sandbox (Testing)
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="is_production"
                                                        id="production" value="1"
                                                        {{ old('is_production', $config->is_production ?? 0) == 1 ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="production">
                                                        Production (Live)
                                                    </label>
                                                </div>
                                            </div>

                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="is_active"
                                                        name="is_active" value="1"
                                                        {{ old('is_active', $config->is_active ?? 1) ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="is_active">
                                                        Aktifkan Gateway
                                                    </label>
                                                </div>
                                                <small class="text-muted">Gateway hanya akan digunakan jika
                                                    diaktifkan</small>
                                            </div>

                                            <div class="d-grid gap-2">
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="ti ti-device-floppy me-2"></i>Simpan Konfigurasi
                                                </button>
                                                <a href="{{ route('dashboard') }}" class="btn btn-outline-secondary">
                                                    <i class="ti ti-arrow-left me-2"></i>Kembali
                                                </a>
                                            </div>
                                        </div>
                                    </div>

                                    @if (isset($config) && $config)
                                        <div class="card mt-3">
                                            <div class="card-header">
                                                <h5 class="card-title mb-0">Status Konfigurasi</h5>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-2">
                                                    <small class="text-muted">Gateway:</small>
                                                    <div class="fw-semibold">{{ ucfirst($config->gateway_name) }}</div>
                                                </div>
                                                <div class="mb-2">
                                                    <small class="text-muted">Environment:</small>
                                                    <div>
                                                        @if ($config->is_production)
                                                            <span class="badge bg-danger">Production</span>
                                                        @else
                                                            <span class="badge bg-warning">Sandbox</span>
                                                        @endif
                                                    </div>
                                                </div>
                                                <div class="mb-2">
                                                    <small class="text-muted">Status:</small>
                                                    <div>
                                                        @if ($config->is_active)
                                                            <span class="badge bg-success">Aktif</span>
                                                        @else
                                                            <span class="badge bg-secondary">Tidak Aktif</span>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
