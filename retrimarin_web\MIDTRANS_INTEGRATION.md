# Midtrans Integration Documentation

## Overview
Implementasi integrasi Midtrans Core API dan pengiriman email otomatis untuk sistem retribusi.

## Features Implemented

### 1. Midtrans Core API Integration
- **Service Class**: `App\Services\MidtransService`
- **Configuration**: Menggunakan data dari `payment_gateway_configs` table
- **Payment Methods**: Mendukung enabled_payments yang dikonfigurasi per metode pembayaran
- **Transaction Creation**: Otomatis membuat transaksi Midtrans saat metode pembayaran = 'midtrans'

### 2. Email Notification System
- **Mail Class**: `App\Mail\RetribusiCreatedMail`
- **Template**: `resources/views/emails/retribusi-created.blade.php`
- **SMTP Configuration**: Menggunakan mail.karpeldevtech.cloud
- **Auto Send**: Email dikirim otomatis ke email perusahaan (jika jenis_pembayar = 'perusahaan')

### 3. Enhanced Retribusi Creation
- **API Endpoint**: `POST /api/petugas/retribusi`
- **Web Endpoint**: `POST /petugas/retribusi`
- **Status Logic**: 
  - Midtrans payment → status 'pending'
  - Other payments → status 'lunas'
- **Error Handling**: Robust error handling dengan logging

## Configuration

### SMTP Settings (.env)
```
MAIL_MAILER=smtp
MAIL_HOST=mail.karpeldevtech.cloud
MAIL_PORT=465
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=owr216he890
MAIL_ENCRYPTION=ssl
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"
```

### Midtrans Configuration
1. Buka `/payment-gateway-config`
2. Pilih Gateway: Midtrans
3. Isi Merchant ID, Client Key, Server Key
4. Pilih Environment (Sandbox/Production)
5. Aktifkan Gateway

### Payment Method Configuration
1. Buka `/metode-pembayaran/create`
2. Pilih Tipe: Midtrans
3. Isi Enabled Payments: `credit_card, gopay, shopeepay, bca_va, bni_va`
4. Simpan

## API Usage

### Create Retribusi with Midtrans
```json
POST /api/petugas/retribusi
Authorization: Bearer {token}
Content-Type: application/json

{
    "jenis_pembayar": "perusahaan",
    "company_id": 1,
    "kapal_id": 1,
    "tarif_id": 1,
    "jumlah": 2,
    "metode_pembayaran_id": 5,
    "keterangan": "Test retribusi with Midtrans"
}
```

### Response with Midtrans
```json
{
    "success": true,
    "message": "Retribusi berhasil dibuat",
    "data": {
        "id": 123,
        "nomor_retribusi": "RTB-**************-001",
        "status_pembayaran": "pending",
        // ... other retribusi data
    },
    "midtrans": {
        "transaction_id": "RTB-**************-001",
        "transaction_status": "pending",
        "payment_type": "bank_transfer",
        "actions": [
            {
                "name": "generate-qr-code",
                "method": "GET",
                "url": "https://api.sandbox.midtrans.com/v2/qris/RTB-**************-001/qr-code"
            }
        ]
    }
}
```

## Email Template Features

### Content Includes:
- Retribusi details (nomor, kapal, tarif, total)
- Company information
- Payment method information
- Midtrans payment link (if applicable)
- Professional styling with responsive design

### Email Recipients:
- Sent to company email if `jenis_pembayar = 'perusahaan'`
- Only sent if company has valid email address
- Includes Midtrans payment information if applicable

## Error Handling

### Midtrans Errors:
- Configuration not found → Warning logged, process continues
- API errors → Error logged, process continues
- Transaction creation fails → Error logged, retribusi still created

### Email Errors:
- SMTP connection fails → Error logged, process continues
- Invalid email address → Error logged, process continues
- Template errors → Error logged, process continues

### Logging:
All events are logged to Laravel log files:
- `Log::info()` for successful operations
- `Log::warning()` for configuration issues
- `Log::error()` for failures

## Testing

### Test Midtrans Integration:
1. Configure payment gateway with Sandbox credentials
2. Create metode pembayaran with type 'midtrans'
3. Create retribusi using Midtrans payment method
4. Check logs for Midtrans API calls
5. Verify transaction status

### Test Email Notification:
1. Ensure company has valid email address
2. Create retribusi with jenis_pembayar = 'perusahaan'
3. Check email delivery
4. Verify email content and formatting

## Troubleshooting

### Common Issues:

1. **Midtrans not configured**
   - Check payment gateway configuration
   - Verify credentials and environment settings

2. **Email not sending**
   - Check SMTP configuration in .env
   - Verify company email address
   - Check Laravel logs for SMTP errors

3. **Transaction creation fails**
   - Check Midtrans credentials
   - Verify enabled_payments configuration
   - Check network connectivity to Midtrans API

### Debug Commands:
```bash
# Check configuration
php artisan config:clear
php artisan cache:clear

# Check logs
tail -f storage/logs/laravel.log

# Test email
php artisan tinker
Mail::raw('Test', function($m) { $m->to('<EMAIL>')->subject('Test'); });
```

## Security Notes

- Server keys are stored securely in database
- Email credentials are in .env file (not in version control)
- All API calls are logged for audit purposes
- Error messages don't expose sensitive information
