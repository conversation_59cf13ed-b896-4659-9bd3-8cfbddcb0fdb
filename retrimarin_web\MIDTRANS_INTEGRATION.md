# Midtrans Integration Documentation

## Overview
Implementasi integrasi Midtrans Core API dan pengiriman email otomatis untuk sistem retribusi.

## Features Implemented

### 1. Midtrans Core API Integration
- **Service Class**: `App\Services\MidtransService`
- **Configuration**: Menggunakan data dari `payment_gateway_configs` table
- **Payment Methods**: Mendukung enabled_payments yang dikonfigurasi per metode pembayaran
- **Transaction Creation**: Otomatis membuat transaksi Midtrans saat metode pembayaran = 'midtrans'

### 2. Email Notification System
- **Mail Class**: `App\Mail\RetribusiCreatedMail`
- **Template**: `resources/views/emails/retribusi-created.blade.php`
- **SMTP Configuration**: Menggunakan mail.karpeldevtech.cloud
- **Auto Send**: Email dikirim otomatis ke email perusahaan (jika jenis_pembayar = 'perusahaan')

### 3. Webhook Handler
- **Controller**: `MidtransWebhookController`
- **Endpoint**: `POST /api/midtrans/notification`
- **Signature Verification**: Otomatis verifikasi menggunakan server key
- **Status Update**: Otomatis update status retribusi berdasarkan notifikasi Midtrans

## Configuration

### SMTP Settings (.env)
```
MAIL_MAILER=smtp
MAIL_HOST=mail.karpeldevtech.cloud
MAIL_PORT=465
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=owr216he890
MAIL_ENCRYPTION=ssl
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"
```

### Midtrans Configuration
1. Buka `/payment-gateway-config`
2. Pilih Gateway: Midtrans
3. Isi Merchant ID, Client Key, Server Key
4. Pilih Environment (Sandbox/Production)
5. Aktifkan Gateway

### Webhook Configuration
Configure this URL in your Midtrans dashboard:
```
https://yourdomain.com/api/midtrans/notification
```

## API Endpoints

### Create Retribusi
```
POST /api/petugas/retribusi
Authorization: Bearer {token}
```

### Webhook Notification
```
POST /api/midtrans/notification
Content-Type: application/json
```

### Check Transaction Status
```
POST /api/midtrans/check-status
Content-Type: application/json
Body: {"order_id": "RTB-20250721123456-001"}
```

## Status Mapping
- `capture` (fraud_status != challenge) → `lunas`
- `capture` (fraud_status = challenge) → `pending`
- `settlement` → `lunas`
- `pending` → `pending`
- `deny`, `cancel`, `expire` → `batal`

## Available Methods

### MidtransService Methods
- `createTransaction($retribusi)` - Create new transaction
- `getTransactionStatus($orderId)` - Get transaction status
- `cancelTransaction($orderId)` - Cancel transaction
- `approveTransaction($orderId)` - Approve challenged transaction
- `denyTransaction($orderId)` - Deny challenged transaction
- `expireTransaction($orderId)` - Expire transaction
- `isConfigured()` - Check if Midtrans is configured

## Error Handling

### Robust Error Handling:
- Configuration not found → Warning logged, process continues
- API errors → Error logged, process continues
- Transaction creation fails → Error logged, retribusi still created
- Email errors → Error logged, process continues

### Logging:
All events are logged to Laravel log files:
- `Log::info()` for successful operations
- `Log::warning()` for configuration issues
- `Log::error()` for failures

## Testing

### Test Commands:
```bash
# Check configuration
php artisan config:clear
php artisan cache:clear

# Test email
php artisan tinker
Mail::raw('Test', function($m) { $m->to('<EMAIL>')->subject('Test'); });

# Check logs
tail -f storage/logs/laravel.log
```

## Security Notes

- Server keys are stored securely in database
- Email credentials are in .env file (not in version control)
- All API calls are logged for audit purposes
- Webhook signature verification prevents unauthorized access
- Error messages don't expose sensitive information
