<?php $__env->startSection('title', 'Konfigurasi WhatsApp Gateway'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fab fa-whatsapp text-success"></i>
            Konfigurasi WhatsApp Gateway
        </h1>
    </div>

    <!-- Alert Messages -->
    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i>
            <?php echo e(session('success')); ?>

            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i>
            <?php echo e(session('error')); ?>

            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    <?php endif; ?>

    <!-- Configuration Card -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-cog"></i>
                        Pengaturan WABlasinGateway
                    </h6>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('whatsapp-gateway-config.update')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        
                        <div class="form-group">
                            <label for="device_token">Device Token</label>
                            <input type="text" 
                                   class="form-control <?php $__errorArgs = ['device_token'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="device_token" 
                                   name="device_token" 
                                   value="<?php echo e(old('device_token', $config->device_token ?? '')); ?>" 
                                   placeholder="Masukkan device token dari WABlasinGateway"
                                   required>
                            <?php $__errorArgs = ['device_token'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <small class="form-text text-muted">
                                Token perangkat yang didapat dari dashboard WABlasinGateway
                            </small>
                        </div>

                        <div class="form-group">
                            <label for="api_key">API Key</label>
                            <input type="text" 
                                   class="form-control <?php $__errorArgs = ['api_key'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="api_key" 
                                   name="api_key" 
                                   value="<?php echo e(old('api_key', $config->api_key ?? '')); ?>" 
                                   placeholder="Masukkan API key dari WABlasinGateway"
                                   required>
                            <?php $__errorArgs = ['api_key'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <small class="form-text text-muted">
                                API key yang didapat dari dashboard WABlasinGateway
                            </small>
                        </div>

                        <div class="form-group">
                            <label for="base_url">Base URL</label>
                            <input type="url" 
                                   class="form-control <?php $__errorArgs = ['base_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="base_url" 
                                   name="base_url" 
                                   value="<?php echo e(old('base_url', $config->base_url ?? 'https://wablasingateway.com/api')); ?>" 
                                   placeholder="https://wablasingateway.com/api"
                                   required>
                            <?php $__errorArgs = ['base_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <small class="form-text text-muted">
                                URL endpoint API WABlasinGateway
                            </small>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" 
                                       class="custom-control-input" 
                                       id="is_active" 
                                       name="is_active" 
                                       <?php echo e(old('is_active', $config->is_active ?? false) ? 'checked' : ''); ?>>
                                <label class="custom-control-label" for="is_active">
                                    Aktifkan WhatsApp Gateway
                                </label>
                            </div>
                            <small class="form-text text-muted">
                                Centang untuk mengaktifkan pengiriman notifikasi WhatsApp
                            </small>
                        </div>

                        <div class="form-group">
                            <label for="description">Deskripsi</label>
                            <textarea class="form-control" 
                                      id="description" 
                                      name="description" 
                                      rows="3" 
                                      placeholder="Deskripsi konfigurasi (opsional)"><?php echo e(old('description', $config->description ?? '')); ?></textarea>
                        </div>

                        <div class="form-group mb-0">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                Simpan Konfigurasi
                            </button>
                            
                            <?php if($config && $config->is_active): ?>
                                <button type="button" class="btn btn-info ml-2" id="testConnectionBtn">
                                    <i class="fas fa-plug"></i>
                                    Test Koneksi
                                </button>
                                
                                <button type="button" class="btn btn-success ml-2" id="sendTestMessageBtn">
                                    <i class="fab fa-whatsapp"></i>
                                    Kirim Test Message
                                </button>
                            <?php endif; ?>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Info Card -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle"></i>
                        Informasi
                    </h6>
                </div>
                <div class="card-body">
                    <?php if($config): ?>
                        <div class="mb-3">
                            <strong>Status:</strong>
                            <span class="badge badge-<?php echo e($config->is_active ? 'success' : 'secondary'); ?>">
                                <?php echo e($config->is_active ? 'Aktif' : 'Tidak Aktif'); ?>

                            </span>
                        </div>
                        
                        <div class="mb-3">
                            <strong>Device Token:</strong><br>
                            <code><?php echo e($config->masked_device_token); ?></code>
                        </div>
                        
                        <div class="mb-3">
                            <strong>API Key:</strong><br>
                            <code><?php echo e($config->masked_api_key); ?></code>
                        </div>
                        
                        <div class="mb-3">
                            <strong>Terakhir Diupdate:</strong><br>
                            <?php echo e($config->updated_at->format('d/m/Y H:i')); ?>

                        </div>
                    <?php else: ?>
                        <p class="text-muted">
                            <i class="fas fa-exclamation-triangle"></i>
                            Belum ada konfigurasi WhatsApp Gateway
                        </p>
                    <?php endif; ?>
                    
                    <hr>
                    
                    <h6 class="font-weight-bold">Cara Mendapatkan Kredensial:</h6>
                    <ol class="small">
                        <li>Daftar di <a href="https://wablasingateway.com" target="_blank">WABlasinGateway.com</a></li>
                        <li>Login ke dashboard</li>
                        <li>Buat device baru</li>
                        <li>Salin Device Token dan API Key</li>
                        <li>Masukkan ke form konfigurasi</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Test Message Modal -->
<div class="modal fade" id="testMessageModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fab fa-whatsapp"></i>
                    Kirim Test Message
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="testMessageForm">
                    <div class="form-group">
                        <label for="test_phone">Nomor WhatsApp</label>
                        <input type="text" class="form-control" id="test_phone" placeholder="628123456789" required>
                        <small class="form-text text-muted">Format: 628xxxxxxxxx (tanpa +)</small>
                    </div>
                    <div class="form-group">
                        <label for="test_message">Pesan</label>
                        <textarea class="form-control" id="test_message" rows="3" required>Halo! Ini adalah test message dari sistem Retrimarin.</textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-success" id="sendTestBtn">
                    <i class="fab fa-whatsapp"></i>
                    Kirim
                </button>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    // Test Connection
    $('#testConnectionBtn').click(function() {
        const btn = $(this);
        const originalText = btn.html();
        
        btn.html('<i class="fas fa-spinner fa-spin"></i> Testing...').prop('disabled', true);
        
        $.ajax({
            url: '<?php echo e(route("whatsapp-gateway-config.test-connection")); ?>',
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                Swal.fire({
                    icon: 'success',
                    title: 'Koneksi Berhasil!',
                    text: response.message,
                    timer: 3000
                });
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                Swal.fire({
                    icon: 'error',
                    title: 'Koneksi Gagal!',
                    text: response.message || 'Terjadi kesalahan'
                });
            },
            complete: function() {
                btn.html(originalText).prop('disabled', false);
            }
        });
    });
    
    // Send Test Message
    $('#sendTestMessageBtn').click(function() {
        $('#testMessageModal').modal('show');
    });
    
    $('#sendTestBtn').click(function() {
        const btn = $(this);
        const originalText = btn.html();
        const phone = $('#test_phone').val();
        const message = $('#test_message').val();
        
        if (!phone || !message) {
            Swal.fire({
                icon: 'warning',
                title: 'Data Tidak Lengkap',
                text: 'Mohon isi nomor WhatsApp dan pesan'
            });
            return;
        }
        
        btn.html('<i class="fas fa-spinner fa-spin"></i> Mengirim...').prop('disabled', true);
        
        $.ajax({
            url: '<?php echo e(route("whatsapp-gateway-config.send-test")); ?>',
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            data: {
                phone_number: phone,
                message: message
            },
            success: function(response) {
                $('#testMessageModal').modal('hide');
                Swal.fire({
                    icon: 'success',
                    title: 'Pesan Terkirim!',
                    text: response.message,
                    timer: 3000
                });
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                Swal.fire({
                    icon: 'error',
                    title: 'Gagal Mengirim!',
                    text: response.message || 'Terjadi kesalahan'
                });
            },
            complete: function() {
                btn.html(originalText).prop('disabled', false);
            }
        });
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp-8.2-new\htdocs\kapal\retrimarin_web\resources\views/admin/whatsapp-gateway-config.blade.php ENDPATH**/ ?>