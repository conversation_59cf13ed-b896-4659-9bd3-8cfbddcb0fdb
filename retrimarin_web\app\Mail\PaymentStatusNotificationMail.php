<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Models\Retribusi;

class PaymentStatusNotificationMail extends Mailable
{
    use Queueable, SerializesModels;

    public $retribusi;
    public $statusType;
    public $transactionDetails;

    /**
     * Create a new message instance.
     */
    public function __construct(Retribusi $retribusi, $statusType, $transactionDetails = null)
    {
        $this->retribusi = $retribusi;
        $this->statusType = $statusType; // 'success', 'cancelled', 'failed'
        $this->transactionDetails = $transactionDetails;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $subjects = [
            'success' => 'Pembayaran Berhasil - ',
            'cancelled' => 'Pembayaran Dibatalkan - ',
            'failed' => 'Pembayaran Gagal - ',
        ];

        $subject = ($subjects[$this->statusType] ?? 'Update Status Pembayaran - ') . $this->retribusi->nomor_retribusi;

        return new Envelope(
            subject: $subject,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.payment-status-notification',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
