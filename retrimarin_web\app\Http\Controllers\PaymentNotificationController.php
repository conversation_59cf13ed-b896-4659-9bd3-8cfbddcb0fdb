<?php

namespace App\Http\Controllers;

use App\Models\Retribusi;
use App\Mail\PaymentStatusNotificationMail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Exception;

class PaymentNotificationController extends Controller
{
    /**
     * Send payment status notification manually
     */
    public function sendStatusNotification(Request $request)
    {
        $request->validate([
            'retribusi_id' => 'required|exists:retribusis,id',
            'status_type' => 'required|in:success,cancelled,failed',
            'message' => 'nullable|string'
        ]);

        try {
            $retribusi = Retribusi::with(['company', 'kapal', 'tarif', 'petugas', 'metodePembayaran'])
                ->findOrFail($request->retribusi_id);

            if (!$retribusi->company || !$retribusi->company->email) {
                return response()->json([
                    'success' => false,
                    'message' => 'Retribusi tidak memiliki email perusahaan'
                ], 400);
            }

            // Send email notification
            Mail::to($retribusi->company->email)->send(
                new PaymentStatusNotificationMail($retribusi, $request->status_type)
            );

            Log::info('Manual payment status notification sent', [
                'retribusi_id' => $retribusi->id,
                'email' => $retribusi->company->email,
                'status_type' => $request->status_type,
                'message' => $request->message
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Notifikasi email berhasil dikirim ke ' . $retribusi->company->email
            ]);

        } catch (Exception $e) {
            Log::error('Failed to send manual payment notification', [
                'retribusi_id' => $request->retribusi_id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Gagal mengirim notifikasi: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send bulk payment notifications
     */
    public function sendBulkNotifications(Request $request)
    {
        $request->validate([
            'retribusi_ids' => 'required|array',
            'retribusi_ids.*' => 'exists:retribusis,id',
            'status_type' => 'required|in:success,cancelled,failed'
        ]);

        $successCount = 0;
        $failedCount = 0;
        $errors = [];

        foreach ($request->retribusi_ids as $retribusiId) {
            try {
                $retribusi = Retribusi::with(['company', 'kapal', 'tarif', 'petugas', 'metodePembayaran'])
                    ->findOrFail($retribusiId);

                if ($retribusi->company && $retribusi->company->email) {
                    Mail::to($retribusi->company->email)->send(
                        new PaymentStatusNotificationMail($retribusi, $request->status_type)
                    );
                    $successCount++;
                } else {
                    $failedCount++;
                    $errors[] = "Retribusi {$retribusi->nomor_retribusi} tidak memiliki email perusahaan";
                }

            } catch (Exception $e) {
                $failedCount++;
                $errors[] = "Retribusi ID {$retribusiId}: " . $e->getMessage();
            }
        }

        Log::info('Bulk payment notifications sent', [
            'total_requested' => count($request->retribusi_ids),
            'success_count' => $successCount,
            'failed_count' => $failedCount,
            'status_type' => $request->status_type
        ]);

        return response()->json([
            'success' => true,
            'message' => "Berhasil mengirim {$successCount} notifikasi, {$failedCount} gagal",
            'details' => [
                'success_count' => $successCount,
                'failed_count' => $failedCount,
                'errors' => $errors
            ]
        ]);
    }

    /**
     * Preview email template
     */
    public function previewEmail(Request $request)
    {
        $request->validate([
            'retribusi_id' => 'required|exists:retribusis,id',
            'status_type' => 'required|in:success,cancelled,failed'
        ]);

        try {
            $retribusi = Retribusi::with(['company', 'kapal', 'tarif', 'petugas', 'metodePembayaran'])
                ->findOrFail($request->retribusi_id);

            $mail = new PaymentStatusNotificationMail($retribusi, $request->status_type);
            
            return view('emails.payment-status-notification', [
                'retribusi' => $retribusi,
                'statusType' => $request->status_type,
                'transactionDetails' => null
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memuat preview: ' . $e->getMessage()
            ], 500);
        }
    }
}
