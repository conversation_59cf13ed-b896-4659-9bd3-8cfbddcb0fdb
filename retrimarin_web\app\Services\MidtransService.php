<?php

namespace App\Services;

use Midtrans\Config;
use Midtrans\CoreApi;
use App\Models\PaymentGatewayConfig;
use Exception;

class MidtransService
{
    protected $config;

    public function __construct()
    {
        $this->config = PaymentGatewayConfig::where('gateway_name', 'midtrans')
            ->where('is_active', true)
            ->first();

        if ($this->config) {
            $this->initializeMidtrans();
        }
    }

    /**
     * Initialize Midtrans configuration
     */
    private function initializeMidtrans()
    {
        Config::$serverKey = $this->config->server_key;
        Config::$isProduction = $this->config->is_production;
        Config::$isSanitized = true;
        Config::$is3ds = true;
    }

    /**
     * Create transaction using Midtrans Core API
     */
    public function createTransaction($retribusi)
    {
        if (!$this->config) {
            throw new Exception('Midtrans configuration not found or inactive');
        }

        // Get enabled payment methods from configuration
        $enabledPayments = [];
        if ($retribusi->metodePembayaran->enabled_payments) {
            $enabledPayments = array_map('trim', explode(',', $retribusi->metodePembayaran->enabled_payments));
        }

        $params = [
            'transaction_details' => [
                'order_id' => $retribusi->nomor_retribusi,
                'gross_amount' => (int) $retribusi->total_harga,
            ],
            'customer_details' => [
                'first_name' => $retribusi->kapal->nama_kapal,
                'email' => $retribusi->company ? $retribusi->company->email : '<EMAIL>',
                'phone' => $retribusi->company ? $retribusi->company->telepon : '',
            ],
            'item_details' => [
                [
                    'id' => $retribusi->tarif->id,
                    'price' => (int) $retribusi->harga_satuan,
                    'quantity' => (int) $retribusi->jumlah,
                    'name' => $retribusi->tarif->nama_tarif,
                    'category' => 'Retribusi',
                ]
            ],
        ];

        // Add enabled payment methods if configured
        if (!empty($enabledPayments)) {
            $params['enabled_payments'] = $enabledPayments;
        }

        // Add custom fields
        $params['custom_field1'] = $retribusi->petugas->nama_petugas;
        $params['custom_field2'] = $retribusi->jenis_pembayar;
        $params['custom_field3'] = $retribusi->company ? $retribusi->company->nama_perusahaan : 'Pribadi';

        try {
            $response = CoreApi::charge($params);
            return $response;
        } catch (Exception $e) {
            throw new Exception('Midtrans API Error: ' . $e->getMessage());
        }
    }

    /**
     * Get transaction status from Midtrans
     */
    public function getTransactionStatus($orderId)
    {
        if (!$this->config) {
            throw new Exception('Midtrans configuration not found or inactive');
        }

        try {
            $response = CoreApi::status($orderId);
            return $response;
        } catch (Exception $e) {
            throw new Exception('Midtrans API Error: ' . $e->getMessage());
        }
    }

    /**
     * Cancel transaction
     */
    public function cancelTransaction($orderId)
    {
        if (!$this->config) {
            throw new Exception('Midtrans configuration not found or inactive');
        }

        try {
            $response = CoreApi::cancel($orderId);
            return $response;
        } catch (Exception $e) {
            throw new Exception('Midtrans API Error: ' . $e->getMessage());
        }
    }

    /**
     * Check if Midtrans is configured and active
     */
    public function isConfigured()
    {
        return $this->config !== null;
    }

    /**
     * Get available payment methods from configuration
     */
    public function getEnabledPaymentMethods()
    {
        if (!$this->config || !$this->config->enabled_payments) {
            return [];
        }

        return array_map('trim', explode(',', $this->config->enabled_payments));
    }
}
