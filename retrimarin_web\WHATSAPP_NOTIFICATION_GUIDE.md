# WhatsApp Notification System - WABlasinGateway Integration

## Overview
Sistem notifikasi WhatsApp otomatis menggunakan WABlasinGateway API untuk mengirim notifikasi saat membuat retribusi baru dan perubahan status pembayaran.

## Features Implemented

### 1. WhatsApp Gateway Configuration
- **Model**: `WhatsappGatewayConfig` untuk menyimpan konfigurasi
- **Controller**: `WhatsappGatewayConfigController` untuk manajemen konfigurasi
- **View**: Interface admin untuk konfigurasi gateway
- **Database**: Tabel `whatsapp_gateway_configs` untuk menyimpan kredensial

### 2. WhatsApp Gateway Service
- **Service Class**: `WhatsappGatewayService` untuk handle semua operasi WhatsApp
- **API Integration**: Integrasi dengan WABlasinGateway API
- **Message Templates**: Template pesan untuk berbagai jenis notifikasi
- **Phone Number Formatting**: Otomatis format nomor telepon ke format internasional

### 3. Automatic Notifications
- **Retribusi Created**: Notifikasi saat retribusi baru dibuat
- **Payment Status Updates**: Notifikasi saat status pembayaran berubah via webhook
- **Both Email & WhatsApp**: Sistem mengirim email dan WhatsApp secara bersamaan

## Configuration

### WABlasinGateway Setup
1. **Daftar di WABlasinGateway**:
   - Kunjungi https://wablasingateway.com
   - Daftar akun baru
   - Login ke dashboard

2. **Buat Device**:
   - Buat device WhatsApp baru
   - Scan QR code dengan WhatsApp
   - Dapatkan Device Token dan API Key

3. **Konfigurasi di Sistem**:
   - Buka `/whatsapp-gateway-config`
   - Masukkan Device Token dan API Key
   - Set Base URL: `https://wablasingateway.com/api`
   - Aktifkan gateway

### Database Configuration
```sql
-- Tabel konfigurasi WhatsApp Gateway
CREATE TABLE whatsapp_gateway_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    gateway_name VARCHAR(255) DEFAULT 'wablasing',
    device_token VARCHAR(255) NOT NULL,
    api_key VARCHAR(255) NOT NULL,
    base_url VARCHAR(255) DEFAULT 'https://wablasingateway.com/api',
    is_active BOOLEAN DEFAULT TRUE,
    description TEXT NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

## API Integration

### WABlasinGateway API Format
```php
$data = array(
    'token' => 'your_device_token',
    'inquiry' => 'send-messages',
    'receiptnumber' => '6285885263097',
    'message' => 'Hello, Arizal!',
    'imageurl' => 'https://example.com/image.jpg', // optional
    'documenturl' => 'https://example.com/document.pdf', // optional
    'apikey' => 'your_api_key'
);
```

### Success Response
```json
{
    "RESULT": "OK",
    "MESSAGE": "Message sent"
}
```

## Message Templates

### 1. Retribusi Created Notification
```
📋 *RETRIMARIN - Notifikasi Retribusi*

Retribusi baru telah dibuat:

📄 *Nomor:* RTB-20250721123456-001
🚢 *Kapal:* KM Sinar Jaya
🏢 *Perusahaan:* PT Pelayaran Nusantara
📊 *Tarif:* Tambat Tetap
📦 *Jumlah:* 2 Hari
💰 *Total:* Rp 500.000
📅 *Tanggal:* 21/07/2025 14:30
👤 *Petugas:* Ahmad Petugas
💳 *Pembayaran:* Midtrans

---
Sistem Manajemen Retribusi Pelabuhan
🌐 Retrimarin
```

### 2. Payment Status Notifications

#### Success (Pembayaran Berhasil)
```
✅ *RETRIMARIN - Pembayaran Berhasil*

Pembayaran untuk retribusi berikut telah berhasil diproses:

📄 *Nomor:* RTB-20250721123456-001
🚢 *Kapal:* KM Sinar Jaya
💰 *Total:* Rp 500.000
📊 *Status:* Lunas
📅 *Tgl Bayar:* 21/07/2025 15:45
💳 *Metode:* Credit Card

✅ Pembayaran telah berhasil diproses
✅ Retribusi telah lunas
✅ Simpan pesan ini sebagai bukti

---
Sistem Manajemen Retribusi Pelabuhan
🌐 Retrimarin
```

#### Cancelled (Pembayaran Dibatalkan)
```
⚠️ *RETRIMARIN - Pembayaran Dibatalkan*

Pembayaran untuk retribusi berikut telah dibatalkan:

📄 *Nomor:* RTB-20250721123456-001
🚢 *Kapal:* KM Sinar Jaya
💰 *Total:* Rp 500.000
📊 *Status:* Batal

⚠️ Pembayaran telah dibatalkan
ℹ️ Hubungi petugas untuk bantuan
ℹ️ Tidak ada biaya yang dikenakan

---
Sistem Manajemen Retribusi Pelabuhan
🌐 Retrimarin
```

#### Failed (Pembayaran Gagal)
```
❌ *RETRIMARIN - Pembayaran Gagal*

Pembayaran untuk retribusi berikut mengalami kegagalan:

📄 *Nomor:* RTB-20250721123456-001
🚢 *Kapal:* KM Sinar Jaya
💰 *Total:* Rp 500.000
📊 *Status:* Pending

❌ Pembayaran mengalami kegagalan
🔄 Silakan coba lagi
📞 Hubungi petugas jika masalah berlanjut

---
Sistem Manajemen Retribusi Pelabuhan
🌐 Retrimarin
```

## Integration Points

### 1. Retribusi Creation
- **API Controller**: `PetugasAuthController@createRetribusi`
- **Web Controller**: `PetugasDashboardController@storeRetribusi`
- **Trigger**: Saat retribusi baru dibuat
- **Condition**: Company memiliki nomor telepon

### 2. Payment Status Updates
- **Webhook Controller**: `MidtransWebhookController@notification`
- **Trigger**: Saat Midtrans mengirim webhook notification
- **Condition**: Status pembayaran berubah dan company memiliki nomor telepon

### 3. Phone Number Formatting
```php
// Input formats yang didukung:
'0812345678'     -> '62812345678'
'+62812345678'   -> '62812345678'
'62812345678'    -> '62812345678'
'08123456789'    -> '628123456789'
```

## Admin Interface

### Configuration Page
- **URL**: `/whatsapp-gateway-config`
- **Features**:
  - Input Device Token dan API Key
  - Set Base URL
  - Toggle aktif/non-aktif
  - Test koneksi
  - Kirim test message
  - View masked credentials

### Test Functions
- **Test Connection**: Verifikasi koneksi ke WABlasinGateway
- **Send Test Message**: Kirim pesan test ke nomor tertentu
- **Real-time Feedback**: Response langsung dari API

## Error Handling

### Graceful Failure
- WhatsApp error tidak menggagalkan proses utama
- Webhook tetap berhasil meski WhatsApp gagal
- Detailed error logging untuk debugging

### Logging
```php
// Success logs
Log::info('WhatsApp notification sent', [
    'retribusi_id' => $retribusi->id,
    'phone' => $retribusi->company->telepon
]);

// Error logs
Log::error('Failed to send WhatsApp notification', [
    'retribusi_id' => $retribusi->id,
    'phone' => $retribusi->company->telepon,
    'error' => $e->getMessage()
]);
```

## Security & Validation

### Credential Security
- Device Token dan API Key disimpan terenkripsi
- Masked display di interface admin
- Secure API calls dengan proper headers

### Phone Number Validation
- Otomatis cleaning dan formatting
- Support multiple input formats
- Indonesian number conversion

## Testing

### Test Configuration
```bash
# Test WhatsApp service
php artisan tinker
$service = new App\Services\WhatsappGatewayService();
$service->isConfigured(); // Check if configured
```

### Test Message Sending
```bash
# Via admin interface
1. Buka /whatsapp-gateway-config
2. Klik "Kirim Test Message"
3. Masukkan nomor dan pesan
4. Klik "Kirim"
```

### Test Integration
```bash
# Create retribusi to test automatic notification
1. Buat retribusi baru dengan company yang memiliki nomor telepon
2. Check logs untuk memastikan WhatsApp terkirim
3. Test webhook dengan mengubah status pembayaran
```

## Troubleshooting

### Common Issues
1. **WhatsApp tidak terkirim**: Check konfigurasi dan nomor telepon company
2. **API Error**: Verify Device Token dan API Key
3. **Phone format error**: Pastikan nomor telepon valid

### Debug Commands
```bash
# Check logs
tail -f storage/logs/laravel.log | grep WhatsApp

# Test service
php artisan tinker
$service = new App\Services\WhatsappGatewayService();
$service->testConnection();
```

## Requirements

### System Requirements
- Company harus memiliki nomor telepon
- WhatsApp Gateway harus dikonfigurasi dan aktif
- Device WhatsApp harus online di WABlasinGateway

### WABlasinGateway Requirements
- Akun aktif di WABlasinGateway
- Device WhatsApp yang terhubung
- Sufficient credit/quota untuk pengiriman pesan
