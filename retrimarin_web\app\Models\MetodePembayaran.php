<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MetodePembayaran extends Model
{
    use HasFactory;

    protected $fillable = [
        'nama_metode',
        'tipe',
        'foto_qris',
        'nama_bank',
        'nomor_rekening',
        'atas_nama',
        'enabled_payments',
        'keterangan',
        'status',
    ];

    // Constants for payment method types
    const TIPE_TUNAI = 'tunai';
    const TIPE_QRIS = 'qris';
    const TIPE_TRANSFER = 'transfer';
    const TIPE_MIDTRANS = 'midtrans';

    // Get all payment method types
    public static function getTipeMetode()
    {
        return [
            self::TIPE_TUNAI => 'Tunai',
            self::TIPE_QRIS => 'QRIS',
            self::TIPE_TRANSFER => 'Transfer Bank',
            self::TIPE_MIDTRANS => 'Midtrans',
        ];
    }

    /**
     * Get tipe metode label.
     */
    public function getTipeMetodeLabelAttribute()
    {
        $labels = self::getTipeMetode();
        return $labels[$this->tipe] ?? $this->tipe;
    }

    /**
     * Get foto QRIS URL.
     */
    public function getFotoQrisUrlAttribute()
    {
        if ($this->foto_qris) {
            return asset('storage/qris/' . $this->foto_qris);
        }
        return null;
    }

    /**
     * Scope a query to only include active metode pembayaran.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to filter by tipe.
     */
    public function scopeByTipe($query, $tipe)
    {
        return $query->where('tipe', $tipe);
    }

    /**
     * Get retribusi records using this metode pembayaran.
     */
    public function retribusis()
    {
        return $this->hasMany(Retribusi::class);
    }
}
