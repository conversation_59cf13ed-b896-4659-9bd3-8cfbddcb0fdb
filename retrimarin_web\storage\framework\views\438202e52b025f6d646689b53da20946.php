<?php $__env->startSection('title', 'Manajemen Batas Wilayah - ' . config('app.name')); ?>

<?php $__env->startSection('content'); ?>
    <!-- Header Fitur -->
    <div class="card bg-light-info shadow-none position-relative overflow-hidden">
        <div class="card-body px-4 py-3">
            <div class="row align-items-center">
                <div class="col-9">
                    <h4 class="fw-semibold mb-8">Manajemen Batas Wilayah</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a class="text-muted" href="<?php echo e(route('dashboard')); ?>">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">Batas Wilayah</li>
                        </ol>
                    </nav>
                </div>
                <div class="col-3">
                    <div class="text-center mb-n5">
                        <img src="<?php echo e(asset('package/dist/images/breadcrumb/ChatBc.png')); ?>" alt=""
                            class="img-fluid mb-n4">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-sm-flex d-block align-items-center justify-content-between mb-9">
                        <div class="mb-3 mb-sm-0">
                            <h5 class="card-title fw-semibold">Daftar Zona Wilayah/Tambat</h5>
                            <p class="card-subtitle mb-0">Kelola berbagai zona wilayah seperti zona tambat, zona bongkar
                                muat, dan zona khusus lainnya</p>
                        </div>
                        <div class="d-flex gap-2">
                            <?php if($wilayahs->count() > 0): ?>
                                <a href="<?php echo e(route('wilayah.map')); ?>" class="btn btn-info">
                                    <i class="ti ti-map me-2"></i>Lihat Peta
                                </a>
                            <?php endif; ?>
                            <a href="<?php echo e(route('wilayah.create')); ?>" class="btn btn-primary">
                                <i class="ti ti-plus me-2"></i>Tambah Zona
                            </a>
                        </div>
                    </div>

                    <?php if(session('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo e(session('success')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if(session('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo e(session('error')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if(session('info')): ?>
                        <div class="alert alert-info alert-dismissible fade show" role="alert">
                            <?php echo e(session('info')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Filter by Zone Type -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <select class="form-select" id="filterJenis" onchange="filterByJenis()">
                                <option value="">Semua Jenis Zona</option>
                                <?php $__currentLoopData = $jenisZona; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($key); ?>" <?php echo e(request('jenis') == $key ? 'selected' : ''); ?>>
                                        <?php echo e($label); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                    </div>

                    <?php if($wilayahs->count() > 0): ?>
                        <div class="table-responsive">
                            <table id="wilayah_datatable" class="table border table-striped table-bordered text-nowrap">
                                <thead>
                                    <tr>
                                        <th>Nama Zona</th>
                                        <th>Jenis Zona</th>
                                        <th>Urutan</th>
                                        <th>Tipe</th>
                                        <th>Warna</th>
                                        <th>Status</th>
                                        <th style="width: 120px;">Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $wilayahs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $wilayah): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <h6 class="fw-semibold mb-0"><?php echo e($wilayah->nama_wilayah); ?></h6>
                                                <?php if($wilayah->deskripsi): ?>
                                                    <small
                                                        class="text-muted"><?php echo e(Str::limit($wilayah->deskripsi, 50)); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span
                                                    class="badge 
                                                    <?php if($wilayah->jenis_zona == 'zona_wilayah'): ?> bg-primary
                                                    <?php elseif($wilayah->jenis_zona == 'zona_tambat'): ?> bg-warning
                                                    <?php elseif($wilayah->jenis_zona == 'zona_bongkar_muat'): ?> bg-info
                                                    <?php else: ?> bg-secondary <?php endif; ?> rounded-3 fw-semibold">
                                                    <?php echo e($wilayah->jenis_zona_label); ?>

                                                </span>
                                            </td>
                                            <td><?php echo e($wilayah->urutan); ?></td>
                                            <td>
                                                <span class="badge bg-light text-dark"><?php echo e(ucfirst($wilayah->tipe)); ?></span>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="color-box me-2"
                                                        style="width: 20px; height: 20px; background-color: <?php echo e($wilayah->warna); ?>; border: 1px solid #ddd; border-radius: 3px;">
                                                    </div>
                                                    <span><?php echo e($wilayah->warna); ?></span>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if($wilayah->is_active): ?>
                                                    <span class="badge bg-success rounded-3 fw-semibold">Aktif</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary rounded-3 fw-semibold">Tidak
                                                        Aktif</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center gap-2">
                                                    <a href="<?php echo e(route('wilayah.show', $wilayah)); ?>"
                                                        class="btn btn-outline-primary btn-sm" title="Lihat Detail">
                                                        <i class="ti ti-eye"></i>
                                                    </a>
                                                    <a href="<?php echo e(route('wilayah.edit', $wilayah)); ?>"
                                                        class="btn btn-outline-warning btn-sm" title="Edit">
                                                        <i class="ti ti-edit"></i>
                                                    </a>
                                                    <form action="<?php echo e(route('wilayah.destroy', $wilayah)); ?>" method="POST"
                                                        class="d-inline"
                                                        onsubmit="return confirm('Apakah Anda yakin ingin menghapus zona ini?')">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('DELETE'); ?>
                                                        <button type="submit" class="btn btn-outline-danger btn-sm"
                                                            title="Hapus">
                                                            <i class="ti ti-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <img src="<?php echo e(asset('package/dist/images/backgrounds/empty-shopping-bag.gif')); ?>" alt=""
                                class="img-fluid mb-4" style="width: 200px;">
                            <h5>Belum Ada Zona Wilayah</h5>
                            <p class="text-muted">Mulai dengan menambahkan zona wilayah pertama Anda</p>
                            <a href="<?php echo e(route('wilayah.create')); ?>" class="btn btn-primary">
                                <i class="ti ti-plus me-2"></i>Tambah Zona Pertama
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        $(document).ready(function() {
            <?php if($wilayahs->count() > 0): ?>
                $('#wilayah_datatable').DataTable({
                    "pageLength": 10,
                    "lengthMenu": [5, 10, 25, 50, 100],
                    "scrollX": true,
                    "autoWidth": false,
                    "language": {
                        "lengthMenu": "Tampilkan _MENU_ data per halaman",
                        "zeroRecords": "Data tidak ditemukan",
                        "info": "Menampilkan halaman _PAGE_ dari _PAGES_",
                        "infoEmpty": "Tidak ada data yang tersedia",
                        "infoFiltered": "(difilter dari _MAX_ total data)",
                        "search": "Cari:",
                        "paginate": {
                            "first": "Pertama",
                            "last": "Terakhir",
                            "next": "Selanjutnya",
                            "previous": "Sebelumnya"
                        }
                    },
                    "columnDefs": [{
                        "targets": [6], // Kolom Aksi
                        "orderable": false,
                        "searchable": false,
                        "width": "120px"
                    }],
                    "order": [
                        [1, 'asc'],
                        [2, 'asc']
                    ] // Default sort by jenis zona then urutan
                });
            <?php endif; ?>
        });

        function filterByJenis() {
            const jenis = document.getElementById('filterJenis').value;
            const url = new URL(window.location);
            if (jenis) {
                url.searchParams.set('jenis', jenis);
            } else {
                url.searchParams.delete('jenis');
            }
            window.location = url;
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp-8.2-new\htdocs\kapal\retrimarin_web\resources\views/wilayah/index.blade.php ENDPATH**/ ?>