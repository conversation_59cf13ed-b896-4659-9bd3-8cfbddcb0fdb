<?php

namespace Database\Seeders;

use App\Models\MetodePembayaran;
use Illuminate\Database\Seeder;

class MetodePembayaranSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        MetodePembayaran::create([
            'nama_metode' => 'Tunai',
            'tipe' => 'tunai',
            'keterangan' => 'Pembayaran secara tunai langsung',
            'status' => 'active',
        ]);

        MetodePembayaran::create([
            'nama_metode' => 'QRIS Bank Mandiri',
            'tipe' => 'qris',
            'keterangan' => 'Pembayaran melalui QRIS Bank Mandiri',
            'status' => 'active',
        ]);

        MetodePembayaran::create([
            'nama_metode' => 'Transfer BCA',
            'tipe' => 'transfer',
            'nama_bank' => 'Bank Central Asia',
            'nomor_rekening' => '**********',
            'atas_nama' => 'PT Retrimarin Indonesia',
            'keterangan' => 'Transfer ke rekening BCA',
            'status' => 'active',
        ]);

        MetodePembayaran::create([
            'nama_metode' => 'Midtrans Payment Gateway',
            'tipe' => 'midtrans',
            'enabled_payments' => 'credit_card, gopay, shopeepay, bca_va, bni_va, bri_va, permata_va, other_va',
            'keterangan' => 'Pembayaran melalui Midtrans dengan berbagai metode',
            'status' => 'active',
        ]);

        MetodePembayaran::create([
            'nama_metode' => 'Transfer Mandiri',
            'tipe' => 'transfer',
            'nama_bank' => 'Bank Mandiri',
            'nomor_rekening' => '**********',
            'atas_nama' => 'PT Retrimarin Indonesia',
            'keterangan' => 'Transfer ke rekening Mandiri',
            'status' => 'active',
        ]);
    }
}
