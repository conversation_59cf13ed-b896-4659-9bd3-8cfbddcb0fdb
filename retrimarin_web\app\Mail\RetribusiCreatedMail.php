<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Models\Retribusi;

class RetribusiCreatedMail extends Mailable
{
    use Queueable, SerializesModels;

    public $retribusi;
    public $midtransResponse;

    /**
     * Create a new message instance.
     */
    public function __construct(Retribusi $retribusi, $midtransResponse = null)
    {
        $this->retribusi = $retribusi;
        $this->midtransResponse = $midtransResponse;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Notifikasi Retribusi Baru - ' . $this->retribusi->nomor_retribusi,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.retribusi-created',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
