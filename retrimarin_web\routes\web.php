<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\KapalController;
use App\Http\Controllers\KategoriKapalController;
use App\Http\Controllers\CompanyController;
use App\Http\Controllers\TarifController;
use App\Http\Controllers\PetugasController;
use App\Http\Controllers\PetugasAuthController;
use App\Http\Controllers\PetugasDashboardController;
use App\Http\Controllers\MetodePembayaranController;
use App\Http\Controllers\PaymentGatewayConfigController;
use App\Http\Controllers\WhatsappGatewayConfigController;
use App\Http\Controllers\WilayahController;
use App\Http\Controllers\TrackingController;
use App\Http\Controllers\AdminRetribusiController;
use App\Http\Controllers\PaymentNotificationController;

Route::get('/', function () {
    return redirect()->route('login');
});

// Dashboard routes (protected by auth middleware)
Route::middleware(['auth'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Company management routes
    Route::resource('company', CompanyController::class);

    // Kategori Kapal management routes
    Route::resource('kategori-kapal', KategoriKapalController::class);

    // Kapal management routes
    Route::resource('kapal', KapalController::class);

    // Tarif management routes
    Route::resource('tarif', TarifController::class);
    Route::get('tarif/satuan/{jenis}', [TarifController::class, 'getSatuanByJenis'])->name('tarif.satuan');

    // Petugas management routes (Admin)
    Route::resource('petugas', PetugasController::class)->parameters([
        'petugas' => 'petugas'
    ]);

    // Metode Pembayaran management routes
    Route::resource('metode-pembayaran', MetodePembayaranController::class);

    // Wilayah management routes
    Route::resource('wilayah', WilayahController::class);
    Route::get('wilayah-map', [WilayahController::class, 'map'])->name('wilayah.map');

    // Payment Gateway Configuration routes
    Route::get('payment-gateway-config', [PaymentGatewayConfigController::class, 'index'])->name('payment-gateway-config.index');
    Route::post('payment-gateway-config', [PaymentGatewayConfigController::class, 'update'])->name('payment-gateway-config.update');

    // WhatsApp Gateway Configuration routes
    Route::get('whatsapp-gateway-config', [WhatsappGatewayConfigController::class, 'index'])->name('whatsapp-gateway-config.index');
    Route::post('whatsapp-gateway-config', [WhatsappGatewayConfigController::class, 'update'])->name('whatsapp-gateway-config.update');
    Route::post('whatsapp-gateway-config/test-connection', [WhatsappGatewayConfigController::class, 'testConnection'])->name('whatsapp-gateway-config.test-connection');
    Route::post('whatsapp-gateway-config/send-test', [WhatsappGatewayConfigController::class, 'sendTestMessage'])->name('whatsapp-gateway-config.send-test');

    // Tracking routes
    Route::prefix('tracking')->name('tracking.')->group(function () {
        Route::get('/', [TrackingController::class, 'index'])->name('index');
        Route::get('/map', [TrackingController::class, 'map'])->name('map');
        Route::get('/export', [TrackingController::class, 'export'])->name('export');
        Route::get('/api/data', [TrackingController::class, 'apiData'])->name('api.data');
        Route::get('/{id}', [TrackingController::class, 'show'])->name('show');
    });

    // Admin Retribusi Monitoring routes
    Route::prefix('admin/retribusi')->name('admin.retribusi.')->group(function () {
        Route::get('/', [AdminRetribusiController::class, 'index'])->name('index');
        Route::get('/statistics', [AdminRetribusiController::class, 'getStatistics'])->name('statistics');
        Route::get('/export', [AdminRetribusiController::class, 'export'])->name('export');
        Route::get('/{retribusi}', [AdminRetribusiController::class, 'show'])->name('show');
    });

    // Payment Notification routes
    Route::post('payment/send-notification', [PaymentNotificationController::class, 'sendStatusNotification'])->name('payment.send-notification');
    Route::post('payment/send-bulk-notifications', [PaymentNotificationController::class, 'sendBulkNotifications'])->name('payment.send-bulk-notifications');
    Route::get('payment/preview-email', [PaymentNotificationController::class, 'previewEmail'])->name('payment.preview-email');
});

// Petugas authentication routes
Route::prefix('petugas-login')->name('petugas.')->group(function () {
    Route::get('/', [PetugasAuthController::class, 'showLoginForm'])->name('login');
    Route::post('/', [PetugasAuthController::class, 'login']);
    Route::post('logout', [PetugasAuthController::class, 'logout'])->name('logout');
});

// Petugas dashboard routes (protected by petugas auth middleware)
Route::middleware(['auth.petugas'])->prefix('petugas-dashboard')->name('petugas.')->group(function () {
    Route::get('/', [PetugasDashboardController::class, 'index'])->name('dashboard');
    Route::get('retribusi', [PetugasDashboardController::class, 'listRetribusi'])->name('retribusi.index');
    Route::get('retribusi/create', [PetugasDashboardController::class, 'createRetribusi'])->name('retribusi.create');
    Route::post('retribusi', [PetugasDashboardController::class, 'storeRetribusi'])->name('retribusi.store');
    Route::get('retribusi/{retribusi}', [PetugasDashboardController::class, 'showRetribusi'])->name('retribusi.show');
    Route::patch('retribusi/{retribusi}/lunas', [PetugasDashboardController::class, 'markAsLunas'])->name('retribusi.lunas');
    Route::patch('retribusi/{retribusi}/cancel', [PetugasDashboardController::class, 'cancelRetribusi'])->name('retribusi.cancel');
    Route::get('tarif-details', [PetugasDashboardController::class, 'getTarifDetails'])->name('tarif.details');
    Route::get('ships-by-payment-type', [PetugasDashboardController::class, 'getShipsByPaymentType'])->name('ships.by.payment.type');
    Route::get('tarifs-by-ship-category', [PetugasDashboardController::class, 'getTarifsByShipCategory'])->name('tarifs.by.ship.category');
});

// require __DIR__ . '/auth.php';
