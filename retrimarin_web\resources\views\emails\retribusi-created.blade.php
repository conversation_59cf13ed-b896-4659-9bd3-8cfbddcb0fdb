<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notifikasi Retribusi Baru</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #2563eb;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #f8fafc;
            padding: 30px;
            border: 1px solid #e2e8f0;
        }
        .footer {
            background-color: #64748b;
            color: white;
            padding: 15px;
            text-align: center;
            border-radius: 0 0 8px 8px;
            font-size: 12px;
        }
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .info-table th,
        .info-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }
        .info-table th {
            background-color: #f1f5f9;
            font-weight: bold;
            width: 40%;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-pending {
            background-color: #fef3c7;
            color: #92400e;
        }
        .status-lunas {
            background-color: #d1fae5;
            color: #065f46;
        }
        .amount {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
            text-align: center;
            margin: 20px 0;
        }
        .payment-info {
            background-color: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .midtrans-info {
            background-color: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚢 Retrimarin</h1>
        <p>Notifikasi Retribusi Baru</p>
    </div>

    <div class="content">
        <h2>Halo {{ $retribusi->company ? $retribusi->company->nama_perusahaan : 'Pemilik Kapal' }},</h2>
        
        <p>Kami informasikan bahwa telah dibuat retribusi baru untuk kapal <strong>{{ $retribusi->kapal->nama_kapal }}</strong>.</p>

        <div class="amount">
            {{ $retribusi->formatted_total_harga }}
        </div>

        <table class="info-table">
            <tr>
                <th>Nomor Retribusi</th>
                <td>{{ $retribusi->nomor_retribusi }}</td>
            </tr>
            <tr>
                <th>Kapal</th>
                <td>{{ $retribusi->kapal->nama_kapal }}</td>
            </tr>
            <tr>
                <th>Jenis Pembayar</th>
                <td>{{ $retribusi->jenis_pembayar_label }}</td>
            </tr>
            @if($retribusi->company)
            <tr>
                <th>Perusahaan</th>
                <td>{{ $retribusi->company->nama_perusahaan }}</td>
            </tr>
            @endif
            <tr>
                <th>Tarif</th>
                <td>{{ $retribusi->tarif->nama_tarif }}</td>
            </tr>
            <tr>
                <th>Jumlah</th>
                <td>{{ $retribusi->jumlah }} {{ $retribusi->tarif->satuan }}</td>
            </tr>
            <tr>
                <th>Harga Satuan</th>
                <td>Rp {{ number_format($retribusi->harga_satuan, 0, ',', '.') }}</td>
            </tr>
            <tr>
                <th>Total Harga</th>
                <td><strong>{{ $retribusi->formatted_total_harga }}</strong></td>
            </tr>
            <tr>
                <th>Status Pembayaran</th>
                <td>
                    <span class="status-badge status-{{ $retribusi->status_pembayaran }}">
                        {{ $retribusi->status_pembayaran_label }}
                    </span>
                </td>
            </tr>
            <tr>
                <th>Metode Pembayaran</th>
                <td>{{ $retribusi->metodePembayaran->nama_metode }}</td>
            </tr>
            <tr>
                <th>Petugas</th>
                <td>{{ $retribusi->petugas->nama_petugas }}</td>
            </tr>
            <tr>
                <th>Tanggal Transaksi</th>
                <td>{{ $retribusi->tanggal_transaksi->format('d/m/Y H:i') }}</td>
            </tr>
            @if($retribusi->keterangan)
            <tr>
                <th>Keterangan</th>
                <td>{{ $retribusi->keterangan }}</td>
            </tr>
            @endif
        </table>

        @if($retribusi->metodePembayaran->tipe == 'midtrans' && isset($midtransResponse))
        <div class="midtrans-info">
            <h3>🔗 Informasi Pembayaran Midtrans</h3>
            <p><strong>Transaction ID:</strong> {{ $midtransResponse->transaction_id ?? 'N/A' }}</p>
            <p><strong>Status:</strong> {{ $midtransResponse->transaction_status ?? 'N/A' }}</p>
            @if(isset($midtransResponse->payment_type))
            <p><strong>Payment Type:</strong> {{ $midtransResponse->payment_type }}</p>
            @endif
            @if(isset($midtransResponse->actions) && is_array($midtransResponse->actions))
            <p><strong>Payment URL:</strong> 
                <a href="{{ $midtransResponse->actions[0]->url ?? '#' }}" target="_blank">
                    Klik untuk melakukan pembayaran
                </a>
            </p>
            @endif
        </div>
        @endif

        <div class="payment-info">
            <h3>📋 Informasi Penting</h3>
            <ul>
                <li>Simpan nomor retribusi ini untuk referensi pembayaran</li>
                <li>Hubungi petugas jika ada pertanyaan mengenai retribusi ini</li>
                @if($retribusi->metodePembayaran->tipe == 'midtrans')
                <li>Untuk pembayaran Midtrans, gunakan link pembayaran yang disediakan</li>
                @endif
                <li>Pembayaran dapat dilakukan melalui metode yang telah ditentukan</li>
            </ul>
        </div>

        <p>Terima kasih atas perhatian dan kerjasamanya.</p>
        
        <p>Salam,<br>
        <strong>Tim Retrimarin</strong></p>
    </div>

    <div class="footer">
        <p>&copy; {{ date('Y') }} Retrimarin. Sistem Manajemen Retribusi Pelabuhan.</p>
        <p>Email ini dikirim secara otomatis, mohon tidak membalas email ini.</p>
    </div>
</body>
</html>
