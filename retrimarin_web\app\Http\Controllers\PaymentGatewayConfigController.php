<?php

namespace App\Http\Controllers;

use App\Models\PaymentGatewayConfig;
use Illuminate\Http\Request;

class PaymentGatewayConfigController extends Controller
{
    /**
     * Display the payment gateway configuration form.
     */
    public function index()
    {
        // Get the current configuration or create default values
        $config = PaymentGatewayConfig::first();

        return view('payment-gateway-config.index', compact('config'));
    }

    /**
     * Update the payment gateway configuration.
     */
    public function update(Request $request)
    {
        $request->validate([
            'gateway_name' => 'required|string|max:255',
            'client_key' => 'required|string|max:255',
            'server_key' => 'required|string|max:255',
            'merchant_id' => 'required|string|max:255',
            'is_production' => 'boolean',
            'is_active' => 'boolean',
            'description' => 'nullable|string',
        ]);

        // Get existing config or create new one
        $config = PaymentGatewayConfig::first();

        if ($config) {
            $config->update($request->all());
            $message = 'Konfigurasi payment gateway berhasil diperbarui.';
        } else {
            PaymentGatewayConfig::create($request->all());
            $message = 'Konfigurasi payment gateway berhasil disimpan.';
        }

        return redirect()->route('payment-gateway-config.index')
            ->with('success', $message);
    }
}
