# Email Notification System - Payment Status Updates

## Overview
Sistem notifikasi email otomatis untuk update status pembayaran retribusi. Email dikirim saat pembayaran berhasil, dibatalkan, atau gagal.

## Features

### 1. Automatic Email Notifications
- **Webhook Integration**: Email dikirim otomatis saat Midtrans mengirim webhook notification
- **Status Changes**: Email dikirim hanya saat status pembayaran berubah
- **Company Email**: Email dikirim ke alamat email perusahaan (jika jenis_pembayar = 'perusahaan')

### 2. Manual Email Notifications
- **Single Notification**: <PERSON>rim notifikasi untuk satu retribusi
- **Bulk Notifications**: <PERSON>rim notifikasi untuk multiple retribusi sekaligus
- **Email Preview**: Preview template email sebelum dikirim

### 3. Email Templates
- **Success Template**: Template untuk pembayaran berhasil (status: lunas)
- **Cancelled Template**: Template untuk pembayaran dibatalkan (status: batal)
- **Failed Template**: Template untuk pembayaran gagal (status: pending)

## Email Types & Status Mapping

### Status Mapping
| Retribusi Status | Email Type | Template Color | Icon |
|------------------|------------|----------------|------|
| `lunas` | `success` | Green | ✅ |
| `batal` | `cancelled` | Orange | ⚠️ |
| `pending` | `failed` | Red | ❌ |

### Email Content
- **Header**: Branding Retrimarin dengan warna sesuai status
- **Status Info**: Icon dan pesan status yang jelas
- **Retribusi Details**: Nomor, kapal, perusahaan, tarif, total harga
- **Transaction Details**: Info dari Midtrans (jika ada)
- **Important Information**: Panduan sesuai status pembayaran
- **Footer**: Informasi kontak dan disclaimer

## API Endpoints

### 1. Automatic Webhook (Midtrans)
```
POST /api/midtrans/notification
Content-Type: application/json
```
- Otomatis dipanggil oleh Midtrans
- Verifikasi signature
- Update status retribusi
- Kirim email jika status berubah

### 2. Manual Single Notification
```
POST /payment/send-notification
Content-Type: application/json

{
    "retribusi_id": 123,
    "status_type": "success",
    "message": "Optional custom message"
}
```

### 3. Bulk Notifications
```
POST /payment/send-bulk-notifications
Content-Type: application/json

{
    "retribusi_ids": [123, 124, 125],
    "status_type": "cancelled"
}
```

### 4. Email Preview
```
GET /payment/preview-email?retribusi_id=123&status_type=success
```

## Usage Examples

### 1. Webhook Integration (Automatic)
Email dikirim otomatis saat:
- Pembayaran berhasil (capture/settlement)
- Pembayaran dibatalkan (cancel/expire)
- Pembayaran gagal (deny)

### 2. Manual Notification (Admin)
```javascript
// Send single notification
fetch('/payment/send-notification', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
    },
    body: JSON.stringify({
        retribusi_id: 123,
        status_type: 'success'
    })
});

// Send bulk notifications
fetch('/payment/send-bulk-notifications', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
    },
    body: JSON.stringify({
        retribusi_ids: [123, 124, 125],
        status_type: 'cancelled'
    })
});
```

### 3. Email Preview
```html
<a href="/payment/preview-email?retribusi_id=123&status_type=success" target="_blank">
    Preview Email
</a>
```

## Email Template Features

### Dynamic Content
- **Status-based styling**: Warna dan icon berubah sesuai status
- **Conditional sections**: Menampilkan info yang relevan saja
- **Transaction details**: Info Midtrans jika tersedia
- **Responsive design**: Tampil baik di desktop dan mobile

### Template Variables
- `$retribusi`: Object retribusi lengkap dengan relasi
- `$statusType`: Tipe status email ('success', 'cancelled', 'failed')
- `$transactionDetails`: Detail transaksi dari Midtrans (optional)

## Configuration

### SMTP Settings
Pastikan SMTP sudah dikonfigurasi di `.env`:
```
MAIL_MAILER=smtp
MAIL_HOST=mail.karpeldevtech.cloud
MAIL_PORT=465
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=owr216he890
MAIL_ENCRYPTION=ssl
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="Retrimarin"
```

### Requirements
- Company harus memiliki email address
- SMTP configuration harus valid
- Midtrans webhook harus dikonfigurasi (untuk automatic notifications)

## Error Handling

### Graceful Failure
- Email error tidak menggagalkan proses utama
- Semua error dicatat dalam log
- Response tetap success meski email gagal

### Logging
```php
// Success
Log::info('Payment status notification email sent', [
    'retribusi_id' => $retribusi->id,
    'email' => $retribusi->company->email,
    'status_type' => $statusType
]);

// Error
Log::error('Failed to send payment status notification email', [
    'retribusi_id' => $retribusi->id,
    'email' => $retribusi->company->email,
    'error' => $e->getMessage()
]);
```

## Testing

### Test Email Configuration
```bash
php artisan tinker
Mail::raw('Test email', function($m) { 
    $m->to('<EMAIL>')->subject('Test'); 
});
```

### Test Webhook
```bash
# Simulate Midtrans webhook
curl -X POST http://localhost:8000/api/midtrans/notification \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": "RTB-20250721123456-001",
    "transaction_status": "settlement",
    "gross_amount": "100000",
    "signature_key": "valid_signature"
  }'
```

### Test Manual Notification
```bash
# Send manual notification
curl -X POST http://localhost:8000/payment/send-notification \
  -H "Content-Type: application/json" \
  -H "X-CSRF-TOKEN: your_csrf_token" \
  -d '{
    "retribusi_id": 123,
    "status_type": "success"
  }'
```

## Security Notes

- CSRF protection untuk manual notifications
- Email addresses divalidasi
- Error messages tidak expose sensitive data
- All email activities logged for audit

## Troubleshooting

### Common Issues
1. **Email tidak terkirim**: Check SMTP configuration dan company email
2. **Webhook tidak trigger email**: Verify signature dan status change
3. **Template error**: Check template syntax dan variables

### Debug Commands
```bash
# Check logs
tail -f storage/logs/laravel.log

# Test SMTP
php artisan tinker
Mail::raw('Test', function($m) { $m->to('<EMAIL>')->subject('Test'); });

# Clear cache
php artisan config:clear
php artisan view:clear
```
