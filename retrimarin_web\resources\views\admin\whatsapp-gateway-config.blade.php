@extends('layouts.admin')

@section('title', 'Konfigurasi WhatsApp Gateway')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fab fa-whatsapp text-success"></i>
            Konfigurasi WhatsApp Gateway
        </h1>
    </div>

    <!-- Alert Messages -->
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i>
            {{ session('success') }}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i>
            {{ session('error') }}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    @endif

    <!-- Configuration Card -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-cog"></i>
                        Pengaturan WABlasinGateway
                    </h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('whatsapp-gateway-config.update') }}" method="POST">
                        @csrf
                        
                        <div class="form-group">
                            <label for="device_token">Device Token</label>
                            <input type="text" 
                                   class="form-control @error('device_token') is-invalid @enderror" 
                                   id="device_token" 
                                   name="device_token" 
                                   value="{{ old('device_token', $config->device_token ?? '') }}" 
                                   placeholder="Masukkan device token dari WABlasinGateway"
                                   required>
                            @error('device_token')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">
                                Token perangkat yang didapat dari dashboard WABlasinGateway
                            </small>
                        </div>

                        <div class="form-group">
                            <label for="api_key">API Key</label>
                            <input type="text" 
                                   class="form-control @error('api_key') is-invalid @enderror" 
                                   id="api_key" 
                                   name="api_key" 
                                   value="{{ old('api_key', $config->api_key ?? '') }}" 
                                   placeholder="Masukkan API key dari WABlasinGateway"
                                   required>
                            @error('api_key')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">
                                API key yang didapat dari dashboard WABlasinGateway
                            </small>
                        </div>

                        <div class="form-group">
                            <label for="base_url">Base URL</label>
                            <input type="url" 
                                   class="form-control @error('base_url') is-invalid @enderror" 
                                   id="base_url" 
                                   name="base_url" 
                                   value="{{ old('base_url', $config->base_url ?? 'https://wablasingateway.com/api') }}" 
                                   placeholder="https://wablasingateway.com/api"
                                   required>
                            @error('base_url')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">
                                URL endpoint API WABlasinGateway
                            </small>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" 
                                       class="custom-control-input" 
                                       id="is_active" 
                                       name="is_active" 
                                       {{ old('is_active', $config->is_active ?? false) ? 'checked' : '' }}>
                                <label class="custom-control-label" for="is_active">
                                    Aktifkan WhatsApp Gateway
                                </label>
                            </div>
                            <small class="form-text text-muted">
                                Centang untuk mengaktifkan pengiriman notifikasi WhatsApp
                            </small>
                        </div>

                        <div class="form-group">
                            <label for="description">Deskripsi</label>
                            <textarea class="form-control" 
                                      id="description" 
                                      name="description" 
                                      rows="3" 
                                      placeholder="Deskripsi konfigurasi (opsional)">{{ old('description', $config->description ?? '') }}</textarea>
                        </div>

                        <div class="form-group mb-0">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                Simpan Konfigurasi
                            </button>
                            
                            @if($config && $config->is_active)
                                <button type="button" class="btn btn-info ml-2" id="testConnectionBtn">
                                    <i class="fas fa-plug"></i>
                                    Test Koneksi
                                </button>
                                
                                <button type="button" class="btn btn-success ml-2" id="sendTestMessageBtn">
                                    <i class="fab fa-whatsapp"></i>
                                    Kirim Test Message
                                </button>
                            @endif
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Info Card -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle"></i>
                        Informasi
                    </h6>
                </div>
                <div class="card-body">
                    @if($config)
                        <div class="mb-3">
                            <strong>Status:</strong>
                            <span class="badge badge-{{ $config->is_active ? 'success' : 'secondary' }}">
                                {{ $config->is_active ? 'Aktif' : 'Tidak Aktif' }}
                            </span>
                        </div>
                        
                        <div class="mb-3">
                            <strong>Device Token:</strong><br>
                            <code>{{ $config->masked_device_token }}</code>
                        </div>
                        
                        <div class="mb-3">
                            <strong>API Key:</strong><br>
                            <code>{{ $config->masked_api_key }}</code>
                        </div>
                        
                        <div class="mb-3">
                            <strong>Terakhir Diupdate:</strong><br>
                            {{ $config->updated_at->format('d/m/Y H:i') }}
                        </div>
                    @else
                        <p class="text-muted">
                            <i class="fas fa-exclamation-triangle"></i>
                            Belum ada konfigurasi WhatsApp Gateway
                        </p>
                    @endif
                    
                    <hr>
                    
                    <h6 class="font-weight-bold">Cara Mendapatkan Kredensial:</h6>
                    <ol class="small">
                        <li>Daftar di <a href="https://wablasingateway.com" target="_blank">WABlasinGateway.com</a></li>
                        <li>Login ke dashboard</li>
                        <li>Buat device baru</li>
                        <li>Salin Device Token dan API Key</li>
                        <li>Masukkan ke form konfigurasi</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Test Message Modal -->
<div class="modal fade" id="testMessageModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fab fa-whatsapp"></i>
                    Kirim Test Message
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="testMessageForm">
                    <div class="form-group">
                        <label for="test_phone">Nomor WhatsApp</label>
                        <input type="text" class="form-control" id="test_phone" placeholder="628123456789" required>
                        <small class="form-text text-muted">Format: 628xxxxxxxxx (tanpa +)</small>
                    </div>
                    <div class="form-group">
                        <label for="test_message">Pesan</label>
                        <textarea class="form-control" id="test_message" rows="3" required>Halo! Ini adalah test message dari sistem Retrimarin.</textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-success" id="sendTestBtn">
                    <i class="fab fa-whatsapp"></i>
                    Kirim
                </button>
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Test Connection
    $('#testConnectionBtn').click(function() {
        const btn = $(this);
        const originalText = btn.html();
        
        btn.html('<i class="fas fa-spinner fa-spin"></i> Testing...').prop('disabled', true);
        
        $.ajax({
            url: '{{ route("whatsapp-gateway-config.test-connection") }}',
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                Swal.fire({
                    icon: 'success',
                    title: 'Koneksi Berhasil!',
                    text: response.message,
                    timer: 3000
                });
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                Swal.fire({
                    icon: 'error',
                    title: 'Koneksi Gagal!',
                    text: response.message || 'Terjadi kesalahan'
                });
            },
            complete: function() {
                btn.html(originalText).prop('disabled', false);
            }
        });
    });
    
    // Send Test Message
    $('#sendTestMessageBtn').click(function() {
        $('#testMessageModal').modal('show');
    });
    
    $('#sendTestBtn').click(function() {
        const btn = $(this);
        const originalText = btn.html();
        const phone = $('#test_phone').val();
        const message = $('#test_message').val();
        
        if (!phone || !message) {
            Swal.fire({
                icon: 'warning',
                title: 'Data Tidak Lengkap',
                text: 'Mohon isi nomor WhatsApp dan pesan'
            });
            return;
        }
        
        btn.html('<i class="fas fa-spinner fa-spin"></i> Mengirim...').prop('disabled', true);
        
        $.ajax({
            url: '{{ route("whatsapp-gateway-config.send-test") }}',
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            data: {
                phone_number: phone,
                message: message
            },
            success: function(response) {
                $('#testMessageModal').modal('hide');
                Swal.fire({
                    icon: 'success',
                    title: 'Pesan Terkirim!',
                    text: response.message,
                    timer: 3000
                });
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                Swal.fire({
                    icon: 'error',
                    title: 'Gagal Mengirim!',
                    text: response.message || 'Terjadi kesalahan'
                });
            },
            complete: function() {
                btn.html(originalText).prop('disabled', false);
            }
        });
    });
});
</script>
@endpush
