<?php

namespace App\Http\Controllers;

use App\Models\WhatsappGatewayConfig;
use App\Services\WhatsappGatewayService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Exception;

class WhatsappGatewayConfigController extends Controller
{
    /**
     * Display WhatsApp Gateway configuration
     */
    public function index()
    {
        $config = WhatsappGatewayConfig::first();

        return view('admin.whatsapp-gateway-config', compact('config'));
    }

    /**
     * Update WhatsApp Gateway configuration
     */
    public function update(Request $request)
    {
        $request->validate([
            'device_token' => 'required|string',
            'api_key' => 'required|string',
            'base_url' => 'required|url',
            'is_active' => 'boolean',
            'description' => 'nullable|string'
        ]);

        try {
            $config = WhatsappGatewayConfig::first();

            if ($config) {
                $config->update([
                    'device_token' => $request->device_token,
                    'api_key' => $request->api_key,
                    'base_url' => $request->base_url,
                    'is_active' => $request->has('is_active'),
                    'description' => $request->description,
                ]);
            } else {
                $config = WhatsappGatewayConfig::create([
                    'gateway_name' => 'wablasing',
                    'device_token' => $request->device_token,
                    'api_key' => $request->api_key,
                    'base_url' => $request->base_url,
                    'is_active' => $request->has('is_active'),
                    'description' => $request->description,
                ]);
            }

            Log::info('WhatsApp Gateway configuration updated', [
                'config_id' => $config->id,
                'is_active' => $config->is_active
            ]);

            return redirect()->route('whatsapp-gateway-config.index')
                ->with('success', 'Konfigurasi WhatsApp Gateway berhasil disimpan');
        } catch (Exception $e) {
            Log::error('Failed to update WhatsApp Gateway configuration', [
                'error' => $e->getMessage()
            ]);

            return redirect()->route('whatsapp-gateway-config.index')
                ->with('error', 'Gagal menyimpan konfigurasi: ' . $e->getMessage());
        }
    }

    /**
     * Test WhatsApp Gateway connection
     */
    public function testConnection(Request $request)
    {
        try {
            $service = new WhatsappGatewayService();

            if (!$service->isConfigured()) {
                return response()->json([
                    'success' => false,
                    'message' => 'WhatsApp Gateway belum dikonfigurasi'
                ], 400);
            }

            $result = $service->testConnection();

            Log::info('WhatsApp Gateway connection test', [
                'result' => $result
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Koneksi WhatsApp Gateway berhasil',
                'data' => $result
            ]);
        } catch (Exception $e) {
            Log::error('WhatsApp Gateway connection test failed', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Koneksi gagal: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send test message
     */
    public function sendTestMessage(Request $request)
    {
        $request->validate([
            'phone_number' => 'required|string',
            'message' => 'required|string'
        ]);

        try {
            $service = new WhatsappGatewayService();

            if (!$service->isConfigured()) {
                return response()->json([
                    'success' => false,
                    'message' => 'WhatsApp Gateway belum dikonfigurasi'
                ], 400);
            }

            $result = $service->sendMessage(
                $request->phone_number,
                $request->message
            );

            return response()->json([
                'success' => true,
                'message' => 'Pesan test berhasil dikirim',
                'data' => $result
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengirim pesan: ' . $e->getMessage()
            ], 500);
        }
    }
}
