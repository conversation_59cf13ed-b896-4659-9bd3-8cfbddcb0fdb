<?php $__env->startSection('title', 'Konfigurasi Payment Gateway'); ?>

<?php $__env->startSection('content'); ?>
    <div class="container-fluid">
        <div class="card bg-light-info shadow-none position-relative overflow-hidden">
            <div class="card-body px-4 py-3">
                <div class="row align-items-center">
                    <div class="col-9">
                        <h4 class="fw-semibold mb-8">Konfigurasi Payment Gateway</h4>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a class="text-muted"
                                        href="<?php echo e(route('dashboard')); ?>">Dashboard</a>
                                </li>
                                <li class="breadcrumb-item" aria-current="page">Konfigurasi Payment Gateway</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="col-3">
                        <div class="text-center mb-n5">
                            <img src="<?php echo e(asset('package/dist/images/breadcrumb/ChatBc.png')); ?>" alt=""
                                class="img-fluid mb-n4">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <?php if(session('success')): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo e(session('success')); ?>

                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo e(session('error')); ?>

                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-body">
                        <div class="mb-4">
                            <h5 class="card-title fw-semibold">Konfigurasi Payment Gateway</h5>
                            <p class="card-subtitle mb-0">Atur konfigurasi payment gateway untuk sistem pembayaran
                                retribusi.
                                Konfigurasi ini akan digunakan untuk semua transaksi pembayaran melalui gateway.</p>
                        </div>

                        <form action="<?php echo e(route('payment-gateway-config.update')); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <div class="row">
                                <div class="col-lg-8">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="card-title mb-0">Informasi Gateway</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="gateway_name" class="form-label">Nama Gateway <span
                                                                class="text-danger">*</span></label>
                                                        <select
                                                            class="form-select <?php $__errorArgs = ['gateway_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                            id="gateway_name" name="gateway_name" required>
                                                            <option value="">Pilih Gateway</option>
                                                            <option value="midtrans"
                                                                <?php echo e(old('gateway_name', $config->gateway_name ?? '') == 'midtrans' ? 'selected' : ''); ?>>
                                                                Midtrans</option>
                                                        </select>
                                                        <?php $__errorArgs = ['gateway_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="merchant_id" class="form-label">Merchant ID <span
                                                                class="text-danger">*</span></label>
                                                        <input type="text"
                                                            class="form-control <?php $__errorArgs = ['merchant_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                            id="merchant_id" name="merchant_id"
                                                            value="<?php echo e(old('merchant_id', $config->merchant_id ?? '')); ?>"
                                                            placeholder="Masukkan Merchant ID" required>
                                                        <?php $__errorArgs = ['merchant_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="client_key" class="form-label">Client Key <span
                                                                class="text-danger">*</span></label>
                                                        <input type="text"
                                                            class="form-control <?php $__errorArgs = ['client_key'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                            id="client_key" name="client_key"
                                                            value="<?php echo e(old('client_key', $config->client_key ?? '')); ?>"
                                                            placeholder="Masukkan Client Key" required>
                                                        <?php $__errorArgs = ['client_key'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="server_key" class="form-label">Server Key <span
                                                                class="text-danger">*</span></label>
                                                        <input type="text"
                                                            class="form-control <?php $__errorArgs = ['server_key'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                            id="server_key" name="server_key"
                                                            value="<?php echo e(old('server_key', $config->server_key ?? '')); ?>"
                                                            placeholder="Masukkan Server Key" required>
                                                        <?php $__errorArgs = ['server_key'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="mb-3">
                                                <label for="description" class="form-label">Deskripsi</label>
                                                <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="description" name="description"
                                                    rows="3" placeholder="Masukkan deskripsi konfigurasi (opsional)"><?php echo e(old('description', $config->description ?? '')); ?></textarea>
                                                <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-lg-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="card-title mb-0">Pengaturan</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label class="form-label">Environment</label>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="is_production"
                                                        id="sandbox" value="0"
                                                        <?php echo e(old('is_production', $config->is_production ?? 0) == 0 ? 'checked' : ''); ?>>
                                                    <label class="form-check-label" for="sandbox">
                                                        Sandbox (Testing)
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="is_production"
                                                        id="production" value="1"
                                                        <?php echo e(old('is_production', $config->is_production ?? 0) == 1 ? 'checked' : ''); ?>>
                                                    <label class="form-check-label" for="production">
                                                        Production (Live)
                                                    </label>
                                                </div>
                                            </div>

                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="is_active"
                                                        name="is_active" value="1"
                                                        <?php echo e(old('is_active', $config->is_active ?? 1) ? 'checked' : ''); ?>>
                                                    <label class="form-check-label" for="is_active">
                                                        Aktifkan Gateway
                                                    </label>
                                                </div>
                                                <small class="text-muted">Gateway hanya akan digunakan jika
                                                    diaktifkan</small>
                                            </div>

                                            <div class="d-grid gap-2">
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="ti ti-device-floppy me-2"></i>Simpan Konfigurasi
                                                </button>
                                                <a href="<?php echo e(route('dashboard')); ?>" class="btn btn-outline-secondary">
                                                    <i class="ti ti-arrow-left me-2"></i>Kembali
                                                </a>
                                            </div>
                                        </div>
                                    </div>

                                    <?php if(isset($config) && $config): ?>
                                        <div class="card mt-3">
                                            <div class="card-header">
                                                <h5 class="card-title mb-0">Status Konfigurasi</h5>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-2">
                                                    <small class="text-muted">Gateway:</small>
                                                    <div class="fw-semibold"><?php echo e(ucfirst($config->gateway_name)); ?></div>
                                                </div>
                                                <div class="mb-2">
                                                    <small class="text-muted">Environment:</small>
                                                    <div>
                                                        <?php if($config->is_production): ?>
                                                            <span class="badge bg-danger">Production</span>
                                                        <?php else: ?>
                                                            <span class="badge bg-warning">Sandbox</span>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                                <div class="mb-2">
                                                    <small class="text-muted">Status:</small>
                                                    <div>
                                                        <?php if($config->is_active): ?>
                                                            <span class="badge bg-success">Aktif</span>
                                                        <?php else: ?>
                                                            <span class="badge bg-secondary">Tidak Aktif</span>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp-8.2-new\htdocs\kapal\retrimarin_web\resources\views/payment-gateway-config/index.blade.php ENDPATH**/ ?>