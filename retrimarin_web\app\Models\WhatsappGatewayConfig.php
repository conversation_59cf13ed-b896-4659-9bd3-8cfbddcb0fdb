<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WhatsappGatewayConfig extends Model
{
    use HasFactory;9

    protected $fillable = [
        'gateway_name',
        'device_token',
        'api_key',
        'base_url',
        'is_active',
        'description',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get masked device token for display
     */
    public function getMaskedDeviceTokenAttribute()
    {
        if (strlen($this->device_token) <= 8) {
            return $this->device_token;
        }
        return substr($this->device_token, 0, 4) . str_repeat('*', strlen($this->device_token) - 8) . substr($this->device_token, -4);
    }

    /**
     * Get masked API key for display
     */
    public function getMaskedApiKeyAttribute()
    {
        if (strlen($this->api_key) <= 8) {
            return $this->api_key;
        }
        return substr($this->api_key, 0, 4) . str_repeat('*', strlen($this->api_key) - 8) . substr($this->api_key, -4);
    }

    /**
     * Scope for active configurations
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
