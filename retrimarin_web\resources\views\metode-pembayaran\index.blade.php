@extends('layouts.app')

@section('title', 'Metode Pembayaran - ' . config('app.name'))

@section('content')
    <!-- Header Fitur -->
    <div class="card bg-light-info shadow-none position-relative overflow-hidden">
        <div class="card-body px-4 py-3">
            <div class="row align-items-center">
                <div class="col-9">
                    <h4 class="fw-semibold mb-8">Metode Pembayaran</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a class="text-muted" href="{{ route('dashboard') }}">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">Metode Pembayaran</li>
                        </ol>
                    </nav>
                </div>
                <div class="col-3">
                    <div class="text-center mb-n5">
                        <img src="{{ asset('package/dist/images/breadcrumb/ChatBc.png') }}" alt=""
                            class="img-fluid mb-n4">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-sm-flex d-block align-items-center justify-content-between mb-9">
                        <div class="mb-3 mb-sm-0">
                            <h5 class="card-title fw-semibold">Daftar Metode Pembayaran</h5>
                            <p class="card-subtitle mb-0">Kelola metode pembayaran yang tersedia untuk retribusi</p>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="{{ route('metode-pembayaran.create') }}" class="btn btn-primary">
                                <i class="ti ti-plus me-2"></i>Tambah Metode
                            </a>
                        </div>
                    </div>

                    @if ($metodePembayarans->count() > 0)
                        <div class="table-responsive">
                            <table id="metode_datatable" class="table border table-striped table-bordered text-nowrap">
                                <thead>
                                    <tr>
                                        <th>Nama Metode</th>
                                        <th>Tipe</th>
                                        <th>Detail</th>
                                        <th>Status</th>
                                        <th style="width: 120px;">Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($metodePembayarans as $metode)
                                        <tr>
                                            <td>
                                                <h6 class="fw-semibold mb-0">{{ $metode->nama_metode }}</h6>
                                                @if ($metode->keterangan)
                                                    <small
                                                        class="text-muted">{{ Str::limit($metode->keterangan, 50) }}</small>
                                                @endif
                                            </td>
                                            <td>
                                                <span
                                                    class="badge
                                                @if ($metode->tipe == 'tunai') bg-success
                                                @elseif($metode->tipe == 'qris') bg-info
                                                @elseif($metode->tipe == 'midtrans') bg-primary
                                                @else bg-warning @endif rounded-3 fw-semibold">
                                                    {{ $metode->tipe_metode_label }}
                                                </span>
                                            </td>
                                            <td>
                                                @if ($metode->tipe == 'transfer')
                                                    <strong>{{ $metode->nama_bank }}</strong><br>
                                                    <small>{{ $metode->nomor_rekening }}</small><br>
                                                    <small>a.n {{ $metode->atas_nama }}</small>
                                                @elseif ($metode->tipe == 'qris')
                                                    @if ($metode->foto_qris)
                                                        <span class="badge bg-success">QRIS Tersedia</span>
                                                    @else
                                                        <span class="badge bg-danger">QRIS Belum Upload</span>
                                                    @endif
                                                @elseif ($metode->tipe == 'midtrans')
                                                    @if ($metode->enabled_payments)
                                                        @php
                                                            $payments = explode(',', $metode->enabled_payments);
                                                            $paymentCount = count($payments);
                                                        @endphp
                                                        <small class="text-muted">{{ $paymentCount }} metode
                                                            aktif</small><br>
                                                        <small>{{ Str::limit($metode->enabled_payments, 30) }}</small>
                                                    @else
                                                        <span class="badge bg-warning">Belum Dikonfigurasi</span>
                                                    @endif
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if ($metode->status == 'active')
                                                    <span class="badge bg-success rounded-3 fw-semibold">Aktif</span>
                                                @else
                                                    <span class="badge bg-danger rounded-3 fw-semibold">Tidak Aktif</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="d-flex gap-1">
                                                    <a href="{{ route('metode-pembayaran.show', $metode->id) }}"
                                                        class="btn btn-sm btn-outline-info" title="Detail">
                                                        <i class="ti ti-eye"></i>
                                                    </a>
                                                    <a href="{{ route('metode-pembayaran.edit', $metode->id) }}"
                                                        class="btn btn-sm btn-outline-warning" title="Edit">
                                                        <i class="ti ti-edit"></i>
                                                    </a>
                                                    <form action="{{ route('metode-pembayaran.destroy', $metode->id) }}"
                                                        method="POST" class="d-inline"
                                                        onsubmit="return confirm('Apakah Anda yakin ingin menghapus metode pembayaran ini?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-outline-danger"
                                                            title="Hapus">
                                                            <i class="ti ti-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="d-flex flex-column align-items-center justify-content-center py-5">
                            <img src="{{ asset('package/dist/images/backgrounds/empty-shopping-cart.gif') }}"
                                alt="" class="img-fluid mb-4" width="200">
                            <h5 class="fw-semibold">Belum ada metode pembayaran</h5>
                            <p class="text-muted text-center">Silakan tambah metode pembayaran untuk memulai transaksi
                                retribusi</p>
                            <a href="{{ route('metode-pembayaran.create') }}" class="btn btn-primary">
                                <i class="ti ti-plus me-2"></i>Tambah Metode Pertama
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    @if ($metodePembayarans->count() > 0)
        <script>
            $(document).ready(function() {
                $('#metode_datatable').DataTable({
                    "pageLength": 25,
                    "responsive": true,
                    "language": {
                        "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Indonesian.json"
                    },
                    "order": [
                        [1, "asc"],
                        [0, "asc"]
                    ]
                });
            });
        </script>
    @endif
@endsection
