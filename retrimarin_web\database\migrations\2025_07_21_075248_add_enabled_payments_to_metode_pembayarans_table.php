<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('metode_pembayarans', function (Blueprint $table) {
            // Update enum to include midtrans
            $table->dropColumn('tipe');
        });

        Schema::table('metode_pembayarans', function (Blueprint $table) {
            $table->enum('tipe', ['tunai', 'qris', 'transfer', 'midtrans'])->after('nama_metode');
            $table->text('enabled_payments')->nullable()->after('atas_nama'); // untuk tipe midtrans
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('metode_pembayarans', function (Blueprint $table) {
            $table->dropColumn(['enabled_payments']);
            $table->dropColumn('tipe');
        });

        Schema::table('metode_pembayarans', function (Blueprint $table) {
            $table->enum('tipe', ['tunai', 'qris', 'transfer'])->after('nama_metode');
        });
    }
};
