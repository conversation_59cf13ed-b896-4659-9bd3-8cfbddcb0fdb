<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('whatsapp_gateway_configs', function (Blueprint $table) {
            $table->id();
            $table->string('gateway_name')->default('wablasing');
            $table->string('device_token');
            $table->string('api_key');
            $table->string('base_url')->default('https://wablasingateway.com/api');
            $table->boolean('is_active')->default(true);
            $table->text('description')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whatsapp_gateway_configs');
    }
};
