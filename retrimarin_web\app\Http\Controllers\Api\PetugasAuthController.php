<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Petugas;
use App\Models\Company;
use App\Models\Kapal;
use App\Models\Tarif;
use App\Models\MetodePembayaran;
use App\Models\Retribusi;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class PetugasAuthController extends Controller
{
    public function login(Request $request)
    {
        $request->validate([
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        $petugas = Petugas::where('username', $request->username)->first();

        if (!$petugas || !Hash::check($request->password, $petugas->password)) {
            return response()->json([
                'success' => false,
                'message' => 'Username atau password salah'
            ], 401);
        }

        if ($petugas->status !== 'active') {
            return response()->json([
                'success' => false,
                'message' => 'Akun petugas tidak aktif'
            ], 401);
        }

        // Use the petugas-api guard for API tokens
        $token = $petugas->createToken('petugas-api-token')->plainTextToken;

        return response()->json([
            'success' => true,
            'message' => 'Login berhasil',
            'data' => [
                'token' => $token,
                'petugas' => $petugas
            ]
        ]);
    }

    public function logout(Request $request)
    {
        $request->user()->currentAccessToken()->delete();

        return response()->json([
            'success' => true,
            'message' => 'Logout berhasil'
        ]);
    }

    public function profile(Request $request)
    {
        return response()->json([
            'success' => true,
            'data' => [
                'petugas' => $request->user()
            ]
        ]);
    }

    public function updateProfile(Request $request)
    {
        $request->validate([
            'nama' => 'required|string|max:255',
            'username' => 'required|string|max:255|unique:petugas,username,' . $request->user()->id,
            'email' => 'required|email|unique:petugas,email,' . $request->user()->id,
            'telepon' => 'nullable|string|max:20',
        ]);

        $petugas = $request->user();
        $petugas->update($request->only(['nama', 'username', 'email', 'telepon']));

        return response()->json([
            'success' => true,
            'message' => 'Profil berhasil diperbarui',
            'data' => [
                'petugas' => $petugas->fresh()
            ]
        ]);
    }

    public function changePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required|string',
            'new_password' => 'required|string|min:6',
            'confirm_password' => 'required|string|same:new_password',
        ]);

        $petugas = $request->user();

        if (!Hash::check($request->current_password, $petugas->password)) {
            return response()->json([
                'success' => false,
                'message' => 'Password lama tidak sesuai'
            ], 400);
        }

        $petugas->update([
            'password' => Hash::make($request->new_password)
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Password berhasil diubah'
        ]);
    }

    public function dashboardStats(Request $request)
    {
        $today = Carbon::today();

        $stats = [
            'total_retribusi_today' => Retribusi::whereDate('created_at', $today)
                ->where('petugas_id', $request->user()->id)
                ->count(),
            'total_pendapatan_today' => Retribusi::whereDate('created_at', $today)
                ->where('petugas_id', $request->user()->id)
                ->sum('total_harga'),
            'total_retribusi_bulan_ini' => Retribusi::whereMonth('created_at', $today->month)
                ->whereYear('created_at', $today->year)
                ->where('petugas_id', $request->user()->id)
                ->count(),
            'total_pendapatan_bulan_ini' => Retribusi::whereMonth('created_at', $today->month)
                ->whereYear('created_at', $today->year)
                ->where('petugas_id', $request->user()->id)
                ->sum('total_harga'),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    public function getCompanies(Request $request)
    {
        $companies = Company::active()->orderBy('nama_perusahaan')->get();

        return response()->json([
            'success' => true,
            'data' => $companies
        ]);
    }

    public function getShipsByPaymentType(Request $request)
    {
        $request->validate([
            'jenis_pembayar' => 'required|in:pribadi,perusahaan',
            'company_id' => 'nullable|exists:companies,id',
        ]);

        $query = Kapal::with('kategoriKapal')->active()->orderBy('nama_kapal');

        if ($request->jenis_pembayar === 'pribadi') {
            $query->whereNull('company_id');
        } elseif ($request->jenis_pembayar === 'perusahaan' && $request->company_id) {
            $query->where('company_id', $request->company_id);
        } else {
            return response()->json([]);
        }

        $kapals = $query->get()->map(function ($kapal) {
            return [
                'id' => $kapal->id,
                'nama_kapal' => $kapal->nama_kapal,
                'nomor_imo' => $kapal->nomor_imo,
                'kategori_kapal_id' => $kapal->kategori_kapal_id,
                'kategori_nama' => $kapal->kategoriKapal ? $kapal->kategoriKapal->nama_kategori : null
            ];
        });

        return response()->json($kapals);
    }

    public function getTarifsByShipCategory(Request $request)
    {
        $request->validate([
            'kapal_id' => 'required|exists:kapals,id',
        ]);

        $kapal = Kapal::with('kategoriKapal')->find($request->kapal_id);

        if (!$kapal) {
            return response()->json([]);
        }

        $query = Tarif::active()->orderBy('jenis_tarif')->orderBy('nama_tarif');

        if ($kapal->kategori_kapal_id) {
            $query->whereHas('kategoriKapals', function ($q) use ($kapal) {
                $q->where('kategori_kapal_id', $kapal->kategori_kapal_id);
            });
        } else {
            $query->whereDoesntHave('kategoriKapals');
        }

        $tarifs = $query->get()->map(function ($tarif) {
            return [
                'id' => $tarif->id,
                'nama_tarif' => $tarif->nama_tarif,
                'jenis_tarif' => $tarif->jenis_tarif,
                'jenis_tarif_label' => $tarif->jenis_tarif_label,
                'satuan' => $tarif->satuan,
                'harga' => $tarif->harga,
                'formatted_harga' => $tarif->formatted_harga,
                'display_text' => $tarif->nama_tarif . ' (' . $tarif->jenis_tarif_label . ') - ' . $tarif->formatted_harga . '/' . $tarif->satuan
            ];
        });

        return response()->json($tarifs);
    }

    public function getPaymentMethods(Request $request)
    {
        $methods = MetodePembayaran::active()->orderBy('tipe')->orderBy('nama_metode')->get();

        return response()->json([
            'success' => true,
            'data' => $methods
        ]);
    }

    public function createRetribusi(Request $request)
    {
        $request->validate([
            'jenis_pembayar' => 'required|in:pribadi,perusahaan',
            'company_id' => 'nullable|exists:companies,id',
            'kapal_id' => 'required|exists:kapals,id',
            'tarif_id' => 'required|exists:tarifs,id',
            'jumlah' => 'required|numeric|min:0.01',
            'metode_pembayaran_id' => 'required|exists:metode_pembayarans,id',
            'keterangan' => 'nullable|string',
        ]);

        $tarif = Tarif::find($request->tarif_id);
        $totalBayar = $tarif->harga * $request->jumlah;

        $retribusi = Retribusi::create([
            'nomor_retribusi' => 'RTB-' . date('YmdHis') . '-' . str_pad($request->user()->id, 3, '0', STR_PAD_LEFT),
            'petugas_id' => $request->user()->id,
            'jenis_pembayar' => $request->jenis_pembayar,
            'company_id' => $request->company_id,
            'kapal_id' => $request->kapal_id,
            'tarif_id' => $request->tarif_id,
            'jumlah' => $request->jumlah,
            'harga_satuan' => $tarif->harga,
            'total_harga' => $totalBayar,
            'metode_pembayaran_id' => $request->metode_pembayaran_id,
            'keterangan' => $request->keterangan,
            'status_pembayaran' => 'lunas',
            'tanggal_transaksi' => now(),
            'tanggal_pembayaran' => now(),
        ]);

        $retribusi->load(['petugas', 'company', 'kapal', 'tarif', 'metodePembayaran']);

        return response()->json([
            'success' => true,
            'message' => 'Retribusi berhasil dibuat',
            'data' => $retribusi
        ]);
    }

    public function getRetribusiHistory(Request $request)
    {
        $page = $request->get('page', 1);
        $perPage = $request->get('per_page', 20);

        $retribusis = Retribusi::with(['company', 'kapal', 'tarif', 'metodePembayaran'])
            ->where('petugas_id', $request->user()->id)
            ->orderBy('created_at', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);

        return response()->json([
            'success' => true,
            'data' => $retribusis->items(),
            'pagination' => [
                'current_page' => $retribusis->currentPage(),
                'last_page' => $retribusis->lastPage(),
                'per_page' => $retribusis->perPage(),
                'total' => $retribusis->total(),
            ]
        ]);
    }
}
