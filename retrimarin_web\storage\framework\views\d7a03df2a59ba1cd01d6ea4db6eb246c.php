<?php $__env->startSection('title', 'Metode Pembayaran - ' . config('app.name')); ?>

<?php $__env->startSection('content'); ?>
    <!-- Header Fitur -->
    <div class="card bg-light-info shadow-none position-relative overflow-hidden">
        <div class="card-body px-4 py-3">
            <div class="row align-items-center">
                <div class="col-9">
                    <h4 class="fw-semibold mb-8">Metode Pembayaran</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a class="text-muted" href="<?php echo e(route('dashboard')); ?>">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">Metode Pembayaran</li>
                        </ol>
                    </nav>
                </div>
                <div class="col-3">
                    <div class="text-center mb-n5">
                        <img src="<?php echo e(asset('package/dist/images/breadcrumb/ChatBc.png')); ?>" alt=""
                            class="img-fluid mb-n4">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo e(session('error')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-sm-flex d-block align-items-center justify-content-between mb-9">
                        <div class="mb-3 mb-sm-0">
                            <h5 class="card-title fw-semibold">Daftar Metode Pembayaran</h5>
                            <p class="card-subtitle mb-0">Kelola metode pembayaran yang tersedia untuk retribusi</p>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="<?php echo e(route('metode-pembayaran.create')); ?>" class="btn btn-primary">
                                <i class="ti ti-plus me-2"></i>Tambah Metode
                            </a>
                        </div>
                    </div>

                    <?php if($metodePembayarans->count() > 0): ?>
                        <div class="table-responsive">
                            <table id="metode_datatable" class="table border table-striped table-bordered text-nowrap">
                                <thead>
                                    <tr>
                                        <th>Nama Metode</th>
                                        <th>Tipe</th>
                                        <th>Detail</th>
                                        <th>Status</th>
                                        <th style="width: 120px;">Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $metodePembayarans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $metode): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <h6 class="fw-semibold mb-0"><?php echo e($metode->nama_metode); ?></h6>
                                                <?php if($metode->keterangan): ?>
                                                    <small
                                                        class="text-muted"><?php echo e(Str::limit($metode->keterangan, 50)); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span
                                                    class="badge
                                                <?php if($metode->tipe == 'tunai'): ?> bg-success
                                                <?php elseif($metode->tipe == 'qris'): ?> bg-info
                                                <?php elseif($metode->tipe == 'midtrans'): ?> bg-primary
                                                <?php else: ?> bg-warning <?php endif; ?> rounded-3 fw-semibold">
                                                    <?php echo e($metode->tipe_metode_label); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <?php if($metode->tipe == 'transfer'): ?>
                                                    <strong><?php echo e($metode->nama_bank); ?></strong><br>
                                                    <small><?php echo e($metode->nomor_rekening); ?></small><br>
                                                    <small>a.n <?php echo e($metode->atas_nama); ?></small>
                                                <?php elseif($metode->tipe == 'qris'): ?>
                                                    <?php if($metode->foto_qris): ?>
                                                        <span class="badge bg-success">QRIS Tersedia</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">QRIS Belum Upload</span>
                                                    <?php endif; ?>
                                                <?php elseif($metode->tipe == 'midtrans'): ?>
                                                    <?php if($metode->enabled_payments): ?>
                                                        <?php
                                                            $payments = explode(',', $metode->enabled_payments);
                                                            $paymentCount = count($payments);
                                                        ?>
                                                        <small class="text-muted"><?php echo e($paymentCount); ?> metode
                                                            aktif</small><br>
                                                        <small><?php echo e(Str::limit($metode->enabled_payments, 30)); ?></small>
                                                    <?php else: ?>
                                                        <span class="badge bg-warning">Belum Dikonfigurasi</span>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if($metode->status == 'active'): ?>
                                                    <span class="badge bg-success rounded-3 fw-semibold">Aktif</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger rounded-3 fw-semibold">Tidak Aktif</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="d-flex gap-1">
                                                    <a href="<?php echo e(route('metode-pembayaran.show', $metode->id)); ?>"
                                                        class="btn btn-sm btn-outline-info" title="Detail">
                                                        <i class="ti ti-eye"></i>
                                                    </a>
                                                    <a href="<?php echo e(route('metode-pembayaran.edit', $metode->id)); ?>"
                                                        class="btn btn-sm btn-outline-warning" title="Edit">
                                                        <i class="ti ti-edit"></i>
                                                    </a>
                                                    <form action="<?php echo e(route('metode-pembayaran.destroy', $metode->id)); ?>"
                                                        method="POST" class="d-inline"
                                                        onsubmit="return confirm('Apakah Anda yakin ingin menghapus metode pembayaran ini?')">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('DELETE'); ?>
                                                        <button type="submit" class="btn btn-sm btn-outline-danger"
                                                            title="Hapus">
                                                            <i class="ti ti-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="d-flex flex-column align-items-center justify-content-center py-5">
                            <img src="<?php echo e(asset('package/dist/images/backgrounds/empty-shopping-cart.gif')); ?>"
                                alt="" class="img-fluid mb-4" width="200">
                            <h5 class="fw-semibold">Belum ada metode pembayaran</h5>
                            <p class="text-muted text-center">Silakan tambah metode pembayaran untuk memulai transaksi
                                retribusi</p>
                            <a href="<?php echo e(route('metode-pembayaran.create')); ?>" class="btn btn-primary">
                                <i class="ti ti-plus me-2"></i>Tambah Metode Pertama
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <?php if($metodePembayarans->count() > 0): ?>
        <script>
            $(document).ready(function() {
                $('#metode_datatable').DataTable({
                    "pageLength": 25,
                    "responsive": true,
                    "language": {
                        "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Indonesian.json"
                    },
                    "order": [
                        [1, "asc"],
                        [0, "asc"]
                    ]
                });
            });
        </script>
    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp-8.2-new\htdocs\kapal\retrimarin_web\resources\views/metode-pembayaran/index.blade.php ENDPATH**/ ?>