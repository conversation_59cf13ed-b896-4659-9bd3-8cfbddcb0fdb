<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Status Pembayaran</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            @if($statusType == 'success')
                background-color: #10b981;
            @elseif($statusType == 'cancelled')
                background-color: #f59e0b;
            @else
                background-color: #ef4444;
            @endif
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #f8fafc;
            padding: 30px;
            border: 1px solid #e2e8f0;
        }
        .footer {
            background-color: #64748b;
            color: white;
            padding: 15px;
            text-align: center;
            border-radius: 0 0 8px 8px;
            font-size: 12px;
        }
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .info-table th,
        .info-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }
        .info-table th {
            background-color: #f1f5f9;
            font-weight: bold;
            width: 40%;
        }
        .status-badge {
            display: inline-block;
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-success {
            background-color: #d1fae5;
            color: #065f46;
        }
        .status-cancelled {
            background-color: #fef3c7;
            color: #92400e;
        }
        .status-failed {
            background-color: #fee2e2;
            color: #991b1b;
        }
        .amount {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
            @if($statusType == 'success')
                color: #10b981;
            @elseif($statusType == 'cancelled')
                color: #f59e0b;
            @else
                color: #ef4444;
            @endif
        }
        .status-info {
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            @if($statusType == 'success')
                background-color: #ecfdf5;
                border: 1px solid #bbf7d0;
            @elseif($statusType == 'cancelled')
                background-color: #fffbeb;
                border: 1px solid #fde68a;
            @else
                background-color: #fef2f2;
                border: 1px solid #fecaca;
            @endif
        }
        .icon {
            font-size: 48px;
            margin-bottom: 10px;
        }
        .transaction-details {
            background-color: #f1f5f9;
            border: 1px solid #cbd5e1;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚢 Retrimarin</h1>
        <p>Update Status Pembayaran</p>
    </div>

    <div class="content">
        <h2>Halo {{ $retribusi->company ? $retribusi->company->nama_perusahaan : 'Pemilik Kapal' }},</h2>
        
        <div class="status-info">
            <div class="icon">
                @if($statusType == 'success')
                    ✅
                @elseif($statusType == 'cancelled')
                    ⚠️
                @else
                    ❌
                @endif
            </div>
            <h3>
                @if($statusType == 'success')
                    Pembayaran Berhasil!
                @elseif($statusType == 'cancelled')
                    Pembayaran Dibatalkan
                @else
                    Pembayaran Gagal
                @endif
            </h3>
            <p>
                @if($statusType == 'success')
                    Pembayaran untuk retribusi <strong>{{ $retribusi->nomor_retribusi }}</strong> telah berhasil diproses.
                @elseif($statusType == 'cancelled')
                    Pembayaran untuk retribusi <strong>{{ $retribusi->nomor_retribusi }}</strong> telah dibatalkan.
                @else
                    Pembayaran untuk retribusi <strong>{{ $retribusi->nomor_retribusi }}</strong> mengalami kegagalan.
                @endif
            </p>
        </div>

        <div class="amount">
            {{ $retribusi->formatted_total_harga }}
        </div>

        <table class="info-table">
            <tr>
                <th>Nomor Retribusi</th>
                <td>{{ $retribusi->nomor_retribusi }}</td>
            </tr>
            <tr>
                <th>Kapal</th>
                <td>{{ $retribusi->kapal->nama_kapal }}</td>
            </tr>
            @if($retribusi->company)
            <tr>
                <th>Perusahaan</th>
                <td>{{ $retribusi->company->nama_perusahaan }}</td>
            </tr>
            @endif
            <tr>
                <th>Tarif</th>
                <td>{{ $retribusi->tarif->nama_tarif }}</td>
            </tr>
            <tr>
                <th>Total Harga</th>
                <td><strong>{{ $retribusi->formatted_total_harga }}</strong></td>
            </tr>
            <tr>
                <th>Status Pembayaran</th>
                <td>
                    <span class="status-badge status-{{ $statusType }}">
                        {{ $retribusi->status_pembayaran_label }}
                    </span>
                </td>
            </tr>
            <tr>
                <th>Tanggal Transaksi</th>
                <td>{{ $retribusi->tanggal_transaksi->format('d/m/Y H:i') }}</td>
            </tr>
            @if($retribusi->tanggal_pembayaran)
            <tr>
                <th>Tanggal Pembayaran</th>
                <td>{{ $retribusi->tanggal_pembayaran->format('d/m/Y H:i') }}</td>
            </tr>
            @endif
        </table>

        @if($transactionDetails)
        <div class="transaction-details">
            <h3>📋 Detail Transaksi</h3>
            @if(isset($transactionDetails->transaction_id))
            <p><strong>Transaction ID:</strong> {{ $transactionDetails->transaction_id }}</p>
            @endif
            @if(isset($transactionDetails->payment_type))
            <p><strong>Metode Pembayaran:</strong> {{ ucfirst(str_replace('_', ' ', $transactionDetails->payment_type)) }}</p>
            @endif
            @if(isset($transactionDetails->transaction_time))
            <p><strong>Waktu Transaksi:</strong> {{ $transactionDetails->transaction_time }}</p>
            @endif
            @if(isset($transactionDetails->bank) && $transactionDetails->bank)
            <p><strong>Bank:</strong> {{ strtoupper($transactionDetails->bank) }}</p>
            @endif
        </div>
        @endif

        <div style="background-color: #eff6ff; border: 1px solid #bfdbfe; border-radius: 8px; padding: 15px; margin: 20px 0;">
            <h3>📋 Informasi Penting</h3>
            <ul>
                @if($statusType == 'success')
                <li>Pembayaran Anda telah berhasil diproses</li>
                <li>Retribusi telah lunas dan tercatat dalam sistem</li>
                <li>Simpan email ini sebagai bukti pembayaran</li>
                @elseif($statusType == 'cancelled')
                <li>Pembayaran telah dibatalkan</li>
                <li>Jika Anda ingin melanjutkan pembayaran, silakan hubungi petugas</li>
                <li>Tidak ada biaya yang dikenakan untuk pembatalan ini</li>
                @else
                <li>Pembayaran mengalami kegagalan</li>
                <li>Silakan coba lagi atau hubungi petugas untuk bantuan</li>
                <li>Pastikan saldo atau limit kartu mencukupi</li>
                @endif
                <li>Hubungi petugas jika ada pertanyaan mengenai retribusi ini</li>
            </ul>
        </div>

        <p>
            @if($statusType == 'success')
                Terima kasih atas pembayaran yang telah dilakukan.
            @elseif($statusType == 'cancelled')
                Jika ada pertanyaan mengenai pembatalan ini, silakan hubungi kami.
            @else
                Mohon maaf atas ketidaknyamanan ini. Silakan hubungi kami untuk bantuan lebih lanjut.
            @endif
        </p>
        
        <p>Salam,<br>
        <strong>Tim Retrimarin</strong></p>
    </div>

    <div class="footer">
        <p>&copy; {{ date('Y') }} Retrimarin. Sistem Manajemen Retribusi Pelabuhan.</p>
        <p>Email ini dikirim secara otomatis, mohon tidak membalas email ini.</p>
    </div>
</body>
</html>
