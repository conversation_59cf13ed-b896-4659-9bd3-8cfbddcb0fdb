<?php

namespace App\Http\Controllers;

use App\Models\MetodePembayaran;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class MetodePembayaranController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $metodePembayarans = MetodePembayaran::orderBy('tipe')->orderBy('nama_metode')->get();
        $tipeMetode = MetodePembayaran::getTipeMetode();
        return view('metode-pembayaran.index', compact('metodePembayarans', 'tipeMetode'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $tipeMetode = MetodePembayaran::getTipeMetode();
        return view('metode-pembayaran.create', compact('tipeMetode'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $rules = [
            'nama_metode' => 'required|string|max:255',
            'tipe' => 'required|in:tunai,qris,transfer,midtrans',
            'status' => 'required|in:active,inactive',
            'keterangan' => 'nullable|string',
        ];

        // Add specific validation based on tipe
        if ($request->tipe == 'qris') {
            $rules['foto_qris'] = 'required|image|mimes:jpeg,png,jpg|max:2048';
        } elseif ($request->tipe == 'transfer') {
            $rules['nama_bank'] = 'required|string|max:255';
            $rules['nomor_rekening'] = 'required|string|max:50';
            $rules['atas_nama'] = 'required|string|max:255';
        } elseif ($request->tipe == 'midtrans') {
            $rules['enabled_payments'] = 'required|string';
        }

        $request->validate($rules);

        $data = $request->except('foto_qris');

        // Handle QRIS photo upload
        if ($request->hasFile('foto_qris')) {
            $file = $request->file('foto_qris');
            $filename = time() . '_' . $file->getClientOriginalName();
            $file->storeAs('public/qris', $filename);
            $data['foto_qris'] = $filename;
        }

        MetodePembayaran::create($data);

        return redirect()->route('metode-pembayaran.index')
            ->with('success', 'Metode pembayaran berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(MetodePembayaran $metodePembayaran)
    {
        return view('metode-pembayaran.show', compact('metodePembayaran'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(MetodePembayaran $metodePembayaran)
    {
        $tipeMetode = MetodePembayaran::getTipeMetode();
        return view('metode-pembayaran.edit', compact('metodePembayaran', 'tipeMetode'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, MetodePembayaran $metodePembayaran)
    {
        $rules = [
            'nama_metode' => 'required|string|max:255',
            'tipe' => 'required|in:tunai,qris,transfer,midtrans',
            'status' => 'required|in:active,inactive',
            'keterangan' => 'nullable|string',
        ];

        // Add specific validation based on tipe
        if ($request->tipe == 'qris') {
            $rules['foto_qris'] = 'nullable|image|mimes:jpeg,png,jpg|max:2048';
        } elseif ($request->tipe == 'transfer') {
            $rules['nama_bank'] = 'required|string|max:255';
            $rules['nomor_rekening'] = 'required|string|max:50';
            $rules['atas_nama'] = 'required|string|max:255';
        } elseif ($request->tipe == 'midtrans') {
            $rules['enabled_payments'] = 'required|string';
        }

        $request->validate($rules);

        $data = $request->except('foto_qris');

        // Handle QRIS photo upload
        if ($request->hasFile('foto_qris')) {
            // Delete old photo if exists
            if ($metodePembayaran->foto_qris) {
                Storage::delete('public/qris/' . $metodePembayaran->foto_qris);
            }

            $file = $request->file('foto_qris');
            $filename = time() . '_' . $file->getClientOriginalName();
            $file->storeAs('public/qris', $filename);
            $data['foto_qris'] = $filename;
        }

        $metodePembayaran->update($data);

        return redirect()->route('metode-pembayaran.index')
            ->with('success', 'Metode pembayaran berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(MetodePembayaran $metodePembayaran)
    {
        // Delete QRIS photo if exists
        if ($metodePembayaran->foto_qris) {
            Storage::delete('public/qris/' . $metodePembayaran->foto_qris);
        }

        $metodePembayaran->delete();

        return redirect()->route('metode-pembayaran.index')
            ->with('success', 'Metode pembayaran berhasil dihapus.');
    }
}
