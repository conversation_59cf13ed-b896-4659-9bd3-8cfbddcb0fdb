@extends('layouts.app')

@section('title', 'Detail Metode Pembayaran - ' . config('app.name'))

@section('content')
    <!-- Header Fitur -->
    <div class="card bg-light-info shadow-none position-relative overflow-hidden">
        <div class="card-body px-4 py-3">
            <div class="row align-items-center">
                <div class="col-9">
                    <h4 class="fw-semibold mb-8">Detail Metode Pembayaran</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a class="text-muted" href="{{ route('dashboard') }}">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item"><a class="text-muted"
                                    href="{{ route('metode-pembayaran.index') }}">Metode
                                    Pembayaran</a></li>
                            <li class="breadcrumb-item" aria-current="page">Detail</li>
                        </ol>
                    </nav>
                </div>
                <div class="col-3">
                    <div class="text-center mb-n5">
                        <img src="{{ asset('package/dist/images/breadcrumb/ChatBc.png') }}" alt=""
                            class="img-fluid mb-n4">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between mb-4">
                        <h5 class="card-title fw-semibold mb-0">Informasi Metode Pembayaran</h5>
                        <div class="d-flex gap-2">
                            <a href="{{ route('metode-pembayaran.edit', $metodePembayaran->id) }}" class="btn btn-warning">
                                <i class="ti ti-edit me-2"></i>Edit
                            </a>
                            <form action="{{ route('metode-pembayaran.destroy', $metodePembayaran->id) }}" method="POST"
                                class="d-inline"
                                onsubmit="return confirm('Apakah Anda yakin ingin menghapus metode pembayaran ini?')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger">
                                    <i class="ti ti-trash me-2"></i>Hapus
                                </button>
                            </form>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-semibold" width="40%">Nama Metode:</td>
                                    <td>{{ $metodePembayaran->nama_metode }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold">Tipe:</td>
                                    <td>
                                        <span
                                            class="badge 
                                        @if ($metodePembayaran->tipe == 'tunai') bg-success
                                        @elseif($metodePembayaran->tipe == 'qris') bg-info
                                        @else bg-warning @endif rounded-3 fw-semibold">
                                            {{ $metodePembayaran->tipe_metode_label }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold">Status:</td>
                                    <td>
                                        @if ($metodePembayaran->status == 'active')
                                            <span class="badge bg-success rounded-3 fw-semibold">Aktif</span>
                                        @else
                                            <span class="badge bg-danger rounded-3 fw-semibold">Tidak Aktif</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold">Dibuat:</td>
                                    <td>{{ $metodePembayaran->created_at->format('d/m/Y H:i') }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold">Diperbarui:</td>
                                    <td>{{ $metodePembayaran->updated_at->format('d/m/Y H:i') }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            @if ($metodePembayaran->tipe == 'transfer')
                                <h6 class="fw-semibold mb-3">Detail Transfer</h6>
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-semibold" width="40%">Nama Bank:</td>
                                        <td>{{ $metodePembayaran->nama_bank }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold">No. Rekening:</td>
                                        <td>{{ $metodePembayaran->nomor_rekening }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold">Atas Nama:</td>
                                        <td>{{ $metodePembayaran->atas_nama }}</td>
                                    </tr>
                                </table>
                            @elseif ($metodePembayaran->tipe == 'qris')
                                <h6 class="fw-semibold mb-3">QRIS</h6>
                                @if ($metodePembayaran->foto_qris)
                                    <img src="{{ $metodePembayaran->foto_qris_url }}" alt="QRIS" class="img-thumbnail"
                                        style="max-width: 300px;">
                                @else
                                    <div class="alert alert-warning">
                                        <i class="ti ti-alert-triangle me-2"></i>Foto QRIS belum diupload
                                    </div>
                                @endif
                            @elseif ($metodePembayaran->tipe == 'midtrans')
                                <h6 class="fw-semibold mb-3">Midtrans Configuration</h6>
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-semibold" width="40%">Enabled Payments:</td>
                                        <td>
                                            @if ($metodePembayaran->enabled_payments)
                                                @php
                                                    $payments = explode(',', $metodePembayaran->enabled_payments);
                                                @endphp
                                                @foreach ($payments as $payment)
                                                    <span class="badge bg-primary me-1">{{ trim($payment) }}</span>
                                                @endforeach
                                            @else
                                                <span class="text-muted">Tidak ada metode yang diaktifkan</span>
                                            @endif
                                        </td>
                                    </tr>
                                </table>
                            @endif
                        </div>
                    </div>

                    @if ($metodePembayaran->keterangan)
                        <div class="mt-4">
                            <h6 class="fw-semibold">Keterangan</h6>
                            <p class="text-muted">{{ $metodePembayaran->keterangan }}</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title fw-semibold">Statistik Penggunaan</h5>
                    <div class="text-center py-4">
                        <div class="mb-3">
                            <h3 class="fw-bold text-primary">{{ $metodePembayaran->retribusis()->count() }}</h3>
                            <p class="text-muted mb-0">Total Transaksi</p>
                        </div>
                        <div class="mb-3">
                            <h4 class="fw-bold text-success">
                                Rp
                                {{ number_format($metodePembayaran->retribusis()->where('status_pembayaran', 'lunas')->sum('total_harga'), 0, ',', '.') }}
                            </h4>
                            <p class="text-muted mb-0">Total Pendapatan</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <h5 class="card-title fw-semibold">Aksi Cepat</h5>
                    <div class="d-grid gap-2">
                        <a href="{{ route('metode-pembayaran.edit', $metodePembayaran->id) }}"
                            class="btn btn-outline-warning">
                            <i class="ti ti-edit me-2"></i>Edit Metode
                        </a>
                        @if ($metodePembayaran->status == 'active')
                            <button class="btn btn-outline-secondary" disabled>
                                <i class="ti ti-check me-2"></i>Status Aktif
                            </button>
                        @else
                            <button class="btn btn-outline-success" disabled>
                                <i class="ti ti-x me-2"></i>Status Tidak Aktif
                            </button>
                        @endif
                        <a href="{{ route('metode-pembayaran.index') }}" class="btn btn-outline-primary">
                            <i class="ti ti-arrow-left me-2"></i>Kembali ke Daftar
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
