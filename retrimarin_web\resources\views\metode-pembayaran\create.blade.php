@extends('layouts.app')

@section('title', 'Tambah Metode Pembayaran - ' . config('app.name'))

@section('content')
    <!-- Header Fitur -->
    <div class="card bg-light-info shadow-none position-relative overflow-hidden">
        <div class="card-body px-4 py-3">
            <div class="row align-items-center">
                <div class="col-9">
                    <h4 class="fw-semibold mb-8">Tambah Metode Pembayaran</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a class="text-muted" href="{{ route('dashboard') }}">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item"><a class="text-muted"
                                    href="{{ route('metode-pembayaran.index') }}">Metode
                                    Pembayaran</a></li>
                            <li class="breadcrumb-item" aria-current="page">Tambah Metode</li>
                        </ol>
                    </nav>
                </div>
                <div class="col-3">
                    <div class="text-center mb-n5">
                        <img src="{{ asset('package/dist/images/breadcrumb/ChatBc.png') }}" alt=""
                            class="img-fluid mb-n4">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <h5 class="card-title fw-semibold">Form Tambah Metode Pembayaran</h5>
            <p class="card-subtitle mb-4">Isi form di bawah untuk menambah metode pembayaran baru</p>

            <form action="{{ route('metode-pembayaran.store') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="nama_metode" class="form-label">Nama Metode <span
                                    class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('nama_metode') is-invalid @enderror"
                                id="nama_metode" name="nama_metode" value="{{ old('nama_metode') }}" required>
                            @error('nama_metode')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="tipe" class="form-label">Tipe Metode <span class="text-danger">*</span></label>
                            <select class="form-select @error('tipe') is-invalid @enderror" id="tipe" name="tipe"
                                required>
                                <option value="">Pilih Tipe</option>
                                @foreach ($tipeMetode as $key => $label)
                                    <option value="{{ $key }}" {{ old('tipe') == $key ? 'selected' : '' }}>
                                        {{ $label }}
                                    </option>
                                @endforeach
                            </select>
                            @error('tipe')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- QRIS Section -->
                <div id="qrisSection" style="display: none;">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="foto_qris" class="form-label">Foto QRIS <span
                                        class="text-danger">*</span></label>
                                <input type="file" class="form-control @error('foto_qris') is-invalid @enderror"
                                    id="foto_qris" name="foto_qris" accept="image/*">
                                <small class="form-text text-muted">Format: JPG, PNG, JPEG. Maksimal 2MB</small>
                                @error('foto_qris')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Transfer Section -->
                <div id="transferSection" style="display: none;">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="nama_bank" class="form-label">Nama Bank <span
                                        class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('nama_bank') is-invalid @enderror"
                                    id="nama_bank" name="nama_bank" value="{{ old('nama_bank') }}">
                                @error('nama_bank')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="nomor_rekening" class="form-label">Nomor Rekening <span
                                        class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('nomor_rekening') is-invalid @enderror"
                                    id="nomor_rekening" name="nomor_rekening" value="{{ old('nomor_rekening') }}">
                                @error('nomor_rekening')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="atas_nama" class="form-label">Atas Nama <span
                                        class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('atas_nama') is-invalid @enderror"
                                    id="atas_nama" name="atas_nama" value="{{ old('atas_nama') }}">
                                @error('atas_nama')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Midtrans Section -->
                <div id="midtransSection" style="display: none;">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="enabled_payments" class="form-label">Enabled Payments <span
                                        class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('enabled_payments') is-invalid @enderror"
                                    id="enabled_payments" name="enabled_payments" value="{{ old('enabled_payments') }}"
                                    placeholder="Contoh: credit_card, gopay, shopeepay, bca_va">
                                <small class="form-text text-muted">Masukkan daftar metode pembayaran yang diaktifkan,
                                    dipisahkan dengan koma. Contoh: credit_card, gopay, shopeepay, bca_va</small>
                                @error('enabled_payments')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                            <select class="form-select @error('status') is-invalid @enderror" id="status"
                                name="status" required>
                                <option value="">Pilih Status</option>
                                <option value="active" {{ old('status') == 'active' ? 'selected' : '' }}>Aktif</option>
                                <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>Tidak Aktif
                                </option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="keterangan" class="form-label">Keterangan</label>
                    <textarea class="form-control @error('keterangan') is-invalid @enderror" id="keterangan" name="keterangan"
                        rows="3">{{ old('keterangan') }}</textarea>
                    @error('keterangan')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="ti ti-device-floppy me-2"></i>Simpan
                    </button>
                    <a href="{{ route('metode-pembayaran.index') }}" class="btn btn-outline-secondary">
                        <i class="ti ti-arrow-left me-2"></i>Kembali
                    </a>
                </div>
            </form>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            console.log('Document ready - metode pembayaran create');

            $('#tipe').change(function() {
                const tipe = $(this).val();
                console.log('Tipe selected:', tipe);

                // Hide all sections first
                $('#qrisSection, #transferSection, #midtransSection').hide();

                // Remove required attributes
                $('#foto_qris, #nama_bank, #nomor_rekening, #atas_nama, #enabled_payments').prop('required',
                    false);

                if (tipe === 'qris') {
                    console.log('Showing QRIS section');
                    $('#qrisSection').show();
                    $('#foto_qris').prop('required', true);
                } else if (tipe === 'transfer') {
                    console.log('Showing Transfer section');
                    $('#transferSection').show();
                    $('#nama_bank, #nomor_rekening, #atas_nama').prop('required', true);
                } else if (tipe === 'midtrans') {
                    console.log('Showing Midtrans section');
                    $('#midtransSection').show();
                    $('#enabled_payments').prop('required', true);
                }
            });

            // Initialize on page load
            const currentTipe = $('#tipe').val();
            if (currentTipe) {
                console.log('Initializing with tipe:', currentTipe);
                $('#tipe').trigger('change');
            }

            // Test if elements exist
            console.log('QRIS section exists:', $('#qrisSection').length);
            console.log('Transfer section exists:', $('#transferSection').length);
            console.log('Midtrans section exists:', $('#midtransSection').length);
            console.log('Tipe select exists:', $('#tipe').length);
        });
    </script>
@endsection
